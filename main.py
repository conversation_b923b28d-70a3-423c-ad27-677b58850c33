import os

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from routers import file_translate
from routers import transcribe_audio
from utils.task import TaskManager
import asyncio
import logging
from contextlib import asynccontextmanager
from core.log import setup_logger

log = setup_logger(__name__)
# 使用异步上下文管理器定义应用生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):

    logging.getLogger("httpx").setLevel(logging.WARNING)
    # 启动工作协程
    await TaskManager.start_workers(num_workers=2)
    log.info("任务处理工作协程已启动")
    
    # 启动定期清理任务
    cleanup_task = asyncio.create_task(periodic_cleanup())
    log.info("定期清理任务已启动")
    
    yield  # 应用正常运行
    
    # 应用关闭时取消清理任务
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        log.info("定期清理任务已取消")

os.makedirs("upload", exist_ok=True)
app = FastAPI(lifespan=lifespan)
app.mount("/static", StaticFiles(directory="upload"), name="static")
app.include_router(file_translate.router, prefix="/translate")
app.include_router(transcribe_audio.router)




async def periodic_cleanup():
    """定期清理已完成的任务"""
    while True:
        try:
            await asyncio.sleep(600)  # 每5分钟清理一次
            await TaskManager.clean_completed_tasks()
            # 删除upload目录下，10分钟前的文件
            await TaskManager.clean_upload_files()
            log.info("已清理完成的任务")
        except Exception as e:
            log.error(f"清理任务时出错: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8887)