import os

from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from routers import file_translate
from routers import transcribe_audio
from utils.task import TaskManager
import asyncio
import logging
from contextlib import asynccontextmanager
from core.log import setup_logger

log = setup_logger(__name__)
# 使用异步上下文管理器定义应用生命周期事件
@asynccontextmanager
async def lifespan(app: FastAPI):

    logging.getLogger("httpx").setLevel(logging.WARNING)

    # 启动工作协程
    await TaskManager.start_workers(num_workers=2)
    log.info("任务处理工作协程已启动")

    # 启动任务监控
    from utils.task_monitor import task_monitor
    await task_monitor.start_monitoring(interval=60)  # 每分钟监控一次
    log.info("任务监控已启动")

    # 启动定期清理任务
    cleanup_task = asyncio.create_task(periodic_cleanup())
    log.info("定期清理任务已启动")

    yield  # 应用正常运行

    # 应用关闭时清理资源
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        log.info("定期清理任务已取消")

    # 停止监控
    await task_monitor.stop_monitoring()
    log.info("任务监控已停止")

os.makedirs("upload", exist_ok=True)
app = FastAPI(lifespan=lifespan)
app.mount("/static", StaticFiles(directory="upload"), name="static")
app.include_router(file_translate.router, prefix="/translate")
app.include_router(transcribe_audio.router)




async def periodic_cleanup():
    """定期清理已完成的任务和文件"""
    from utils.task_monitor import task_monitor

    while True:
        try:
            await asyncio.sleep(60)  # 每5分钟清理一次

            # 生成健康报告
            health_report = await task_monitor.get_health_report()
            log.info(f"系统健康状况: {health_report.status.value} (分数: {health_report.score:.1f})")

            if health_report.issues:
                log.warning(f"发现问题: {'; '.join(health_report.issues)}")

            # 检测死锁
            deadlocks = await task_monitor.detect_deadlocks()
            if deadlocks:
                log.error(f"检测到潜在死锁: {'; '.join(deadlocks)}")

            # 执行清理任务
            log.info("开始执行定期清理...")
            await TaskManager.clean_completed_tasks()
            await TaskManager.clean_upload_files()
            log.info("定期清理完成")

        except Exception as e:
            log.error(f"清理任务时出错: {str(e)}", exc_info=True)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8887)