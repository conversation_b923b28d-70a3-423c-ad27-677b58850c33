import asyncio
import uuid

from fastapi import APIRouter, File, UploadFile, Form
from typing import Annotated, Callable
import logging

from utils.office_translate import word_translate_v1 as wt, excel_translate_v1 as et, ppt_translate_v1 as pt, pdf_translate_v1, translate_image
from utils.file import save_file
from utils.task import TaskManager
from schemas.file_translate import TextTranslate, TranslateResponse
from utils.pdf2docx.pdf2docx_translate import translate_pdf_to_docx
from utils.translation import new_translator
# 加载模型
# model = OnnxModel.load_available()
log = logging.getLogger(__name__)
router = APIRouter()

@router.get("/task/{task_id}")
async def get_task_status(task_id: str):
    return await TaskManager.get_task_status(task_id)


# async def async_translate(path: str, output:str,  src: str, dst: str, service: str, model, func, envs={}, prompt="", thread:int=2):
#    try:
#         # 使用loop.run_in_executor来执行同步函数
#         loop = asyncio.get_running_loop()
#         result = await loop.run_in_executor(
#             None,  # 使用默认执行器
#             lambda: func(
#                 file=path,
#                 output=output,
#                 lang_in=src,
#                 lang_out=dst,
#                 service=service,
#                 model=model,
#                 thread=thread,
#                 envs=envs,
#                 prompt=prompt,
#             )
#         )
#         # log.info(f"翻译执行完成，结果类型: {type(result)}")
#         return result
#    except Exception as e:
#         log.error(f"翻译执行失败: {str(e)}", exc_info=True)
#         # 返回一个错误响应而不是抛出异常
#         return {"error": str(e)}

async def async_translate_v1(file:str, output:str, service:str, llm_api: str, llm_api_key: str, llm_model: str, prompt: str,lang_in:str, lang_out:str, thread:int, ignore_cache: bool, func: Callable,ocr_lang:str = False, is_ocr:bool = False, is_translate: bool = False):
    """改进的异步翻译函数，包含文件管理"""
    try:
        # 使用loop.run_in_executor来执行同步函数
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(
            None,  # 使用默认执行器
            lambda: func(
                file=file,
                output=output,
                service=service,
                llm_api=llm_api,
                llm_api_key=llm_api_key,
                llm_model=llm_model,
                lang_in=lang_in,
                lang_out=lang_out,
                thread=thread,
                ignore_cache=ignore_cache,
                prompt=prompt,
                ocr_lang=ocr_lang,
                is_ocr=is_ocr,
                is_translate=is_translate
            )
        )
        # log.info(f"翻译执行完成，结果类型: {type(result)}")
        return result
    except Exception as e:
        log.error(f"翻译执行失败: {str(e)}", exc_info=True)
        # 返回一个错误响应而不是抛出异常
        return {"error": str(e)}
    finally:
        # 任务完成后，取消注册输入文件，允许被清理
        try:
            await TaskManager.unregister_active_file(file)
        except Exception as e:
            log.warning(f"取消注册文件失败: {str(e)}")

@router.post("/pdf")
async def pdf_translate(
        file: Annotated[UploadFile, File(description="需要翻译的PDF")] = None,
        path: Annotated[str, Form(description="PDF的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:openai")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans",
        ocr_lang: Annotated[str, Form(description="OCR 语言")] = "auto",
        is_ocr: Annotated[bool, Form(description="是否OCR")] = False,
        thread: Annotated[int, Form(description="线程数")] = 5,
):
    # 判断传参中的file和path是否为空
    try:
        if not file and not path:
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")
            # return {"code": 0, "message": "file 和 path 不能是空"}

        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)
        task_id = str(uuid.uuid4())
        # 直接创建异步任务，不等待执行完成
        # coro = async_translate(path,"upload", src, dst, service, model, translate, {
        #     "OPENAI_BASE_URL": llm_api,
        #     "OPENAI_API_KEY": llm_api_key,
        #     "OPENAI_MODEL": llm_model,
        # }, llm_prompt)
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst, thread, False, pdf_translate_v1, ocr_lang, is_ocr)
        await TaskManager.create_task(task_id, coro)
        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")

@router.post("/pdf2docx")
async def pdf2docx_translate(
        file: Annotated[UploadFile, File(description="需要翻译的PDF")] = None,
        path: Annotated[str, Form(description="PDF的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:openai")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans",
        ocr_lang: Annotated[str, Form(description="OCR 语言")] = "auto",
        is_translate: Annotated[bool, Form(description="是否翻译")] = False,
        is_ocr: Annotated[bool, Form(description="是否OCR")] = False,
        thread: Annotated[int, Form(description="线程数")] = 2,
):
    # 判断传参中的file和path是否为空
    try:
        if not file and not path:
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")

        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)

        task_id = str(uuid.uuid4())
        # coro = async_translate(path, "upload", src, dst, service, None, translate_pdf_to_docx, {
        #     "OPENAI_BASE_URL": llm_api,
        #     "OPENAI_API_KEY": llm_api_key,
        #     "OPENAI_MODEL": llm_model,
        #     "is_ocr": 1 if is_ocr else 0,
        #     "ocr_lang": ocr_lang,
        #     "is_translate": is_translate,
        # },llm_prompt, thread)
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst,
                                  thread, False, translate_pdf_to_docx, ocr_lang, is_ocr, is_translate)
        await TaskManager.create_task(task_id, coro)
        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")
        


@router.post("/word")
async def word_translate(
        file: Annotated[UploadFile, File(description="需要翻译的Word文档")] = None,
        path: Annotated[str, Form(description="Word文档的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:llm:glm4:latest")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans" ,
        thread: Annotated[int, Form(description="线程数")] = 2
):
      # 判断传参中的file和path是否为空
    if not file and not path:
        return TranslateResponse(code=0, message="file 和 path 不能同时为空")

    try:
        
        path, err = await save_file(file, path)
        log.info(f"文件保存结果：path={path}, err={err}")
        
        if err:
            return TranslateResponse(code=0, message=err)

        task_id = str(uuid.uuid4())
        log.info(f"创建任务ID：{task_id}")
        
        # coro = async_translate(path, "upload", src, dst, service, None, wt, {
        #     "OPENAI_BASE_URL": llm_api,
        #     "OPENAI_API_KEY": llm_api_key,
        #     "OPENAI_MODEL": llm_model,
        # }, llm_prompt)
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst, thread, False, wt)
        await TaskManager.create_task(task_id, coro)
        log.info(f"任务创建成功：{task_id}")
        
        # 直接返回成功结果
        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        log.error(f"创建翻译任务失败: {str(e)}", exc_info=True)
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")


@router.post("/excel")
async def excel_translate(
        file: Annotated[UploadFile, File(description="需要翻译的Excel文档")] = None,
        path: Annotated[str, Form(description="Excel文档的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:llm:glm4:latest")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans" ,
        thread: Annotated[int, Form(description="线程数")] = 2
):
    # 判断传参中的file和path是否为空
    try:
        if not file and not path:
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")

        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)

        task_id = str(uuid.uuid4())
        # coro = async_translate(path, "upload", src, dst, service, None, et, {
        #     "OPENAI_BASE_URL": llm_api,
        #     "OPENAI_API_KEY": llm_api_key,
        #     "OPENAI_MODEL": llm_model,
        # }, llm_prompt)
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst, thread, False, et)
        await TaskManager.create_task(task_id, coro)

        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        log.error(f"创建翻译任务失败: {str(e)}")
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")


@router.post("/pptx")
async def ppt_translate(
        file: Annotated[UploadFile, File(description="需要翻译的PPT文档")] = None,
        path: Annotated[str, Form(description="PPT文档的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:openai")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans" ,
        thread: Annotated[int, Form(description="线程数")] = 2
):
    # 判断传参中的file和path是否为空

    try:
        if not file and not path and path == "":
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")

        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)

        task_id = str(uuid.uuid4())
        # coro = async_translate(path, "upload", src, dst, service, None, pt, {
        #     "OPENAI_BASE_URL": llm_api,
        #     "OPENAI_API_KEY": llm_api_key,
        #     "OPENAI_MODEL": llm_model,
        # }, llm_prompt)
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst, thread, False, pt)
       
        await TaskManager.create_task(task_id, coro)

        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        log.error(f"创建翻译任务失败: {str(e)}")
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")

@router.post("/image")
async def image_translate(
        file: Annotated[UploadFile, File(description="需要翻译的图片")] = None,
        path: Annotated[str, Form(description="图片的本地路径或网络路径")] = "",
        service: Annotated[str, Form(description="翻译服务, 如果是使用大模型，参数就是服务:openai")] = "nllb200",
        llm_api: Annotated[str, Form(description="LLM API")] = "",
        llm_api_key: Annotated[str, Form(description="LLM API KEY")] = "",
        llm_model: Annotated[str, Form(description="LLM 模型")] = "",
        llm_prompt: Annotated[str, Form(description="LLM 提示词")] = "",
        src: Annotated[str, Form(description="源语言")] = "eng_Latn",
        dst: Annotated[str, Form(description="目标语言")] = "zho_Hans",
        ocr_lang: Annotated[str, Form(description="OCR 语言")] = "auto",
        thread: Annotated[int, Form(description="线程数")] = 2,
):
    # 判断传参中的file和path是否为空
    try:
        if not file and not path:
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")
            # return {"code": 0, "message": "file 和 path 不能是空"}

        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)
        task_id = str(uuid.uuid4())
        coro = async_translate_v1(path, "upload", service, llm_api, llm_api_key, llm_model, llm_prompt, src, dst, thread, False, translate_image, ocr_lang, is_ocr=True)
        await TaskManager.create_task(task_id, coro)
        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")

@router.post("/text")
async def text_translate(request: TextTranslate):
    device = new_translator(
       service = request.service,
       lang_in =request.src,
       lang_out = request.dst,
       prompt = request.llm_prompt,
       llm_api = request.llm_api,
       llm_api_key = request.llm_api_key,
       llm_model = request.llm_model,
       thread = request.thread,
    )
    result = device.do_translate(request.text)
    return {
        "code": 1,
        "data": result
    }
