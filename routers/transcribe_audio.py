import logging
from fastapi import APIRouter
from utils.file import save_file
from typing import Annotated
from fastapi import File, Form, UploadFile
import uuid
import asyncio
from utils.transcribe.transcribe import WhisperXTranslator
from utils.task import TaskManager
from schemas.file_translate import TranslateResponse
from schemas.transcribe import nllb200_to_iso


log = logging.getLogger(__name__)
router = APIRouter()

async def async_transcribe_audio(
    file: str,
    output: str,
    language: str = None,
    is_diarize:bool = False,
    wav2vec2: bool = False,
):
    """改进的异步音频转录函数，包含文件管理"""
    wh = WhisperXTranslator(
            file=file,
            output=output,
            language=language,
            is_diarize=is_diarize,
            wav2vec2=wav2vec2,
        )
    try:
        loop = asyncio.get_running_loop()
        result = await loop.run_in_executor(
            None,  # 使用默认执行器
            lambda: wh()
        )
        return result
    except Exception as e:
        log.error(f"音频转录执行失败: {str(e)}", exc_info=True)
        return {"error": str(e)}
    finally:
        # 任务完成后，取消注册输入文件，允许被清理
        try:
            from utils.task import TaskManager
            await TaskManager.unregister_active_file(file)
        except Exception as e:
            log.warning(f"取消注册文件失败: {str(e)}")

@router.post("/transcribe")
async def transcribe_audio(
    file: Annotated[UploadFile, File(description="需要转录的音视频")] = None,
    path: Annotated[str, Form(description="本地路径或网络路径")] = "",
    language: Annotated[str, Form(description="语言")] = "auto",
    is_diarize: Annotated[bool, Form(description="是否进行说话人分离")] = False,
    wav2vec2: Annotated[bool, Form(description="是否使用词级时间戳对齐")] = False,
):
    try:
        if not file and not path:
            return TranslateResponse(code=0, message="file 和 path 不能同时为空")

        print(path, language, is_diarize, wav2vec2)
        path, err = await save_file(file, path)
        if err:
            return TranslateResponse(code=0, message=err)
        task_id = str(uuid.uuid4())
        coro = async_transcribe_audio(
            file=path,
            output="upload",
            language=nllb200_to_iso.get(language, None),
            is_diarize=is_diarize,
            wav2vec2=wav2vec2,
        )
        await TaskManager.create_task(task_id, coro)
        return TranslateResponse(code=1, task_id=task_id, message="翻译任务已提交")
    except Exception as e:
        return TranslateResponse(code=0, message=f"创建翻译任务失败: {str(e)}")