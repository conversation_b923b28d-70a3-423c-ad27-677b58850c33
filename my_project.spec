# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_data_files, collect_submodules
import sys

# 根据平台设置不同的配置
if sys.platform.startswith('win'):
    # Windows特定配置
    platform_binaries = []
    platform_excludes = ['matplotlib']
    exe_name = 'StarMap_Translator.exe'
    icon_file = 'icon.ico'  # 如果有图标文件
else:
    # Linux特定配置
    platform_binaries = []
    platform_excludes = ['win32com', 'pythoncom', 'matplotlib']
    exe_name = 'starmap_translator'
    icon_file = None

block_cipher = None

# 收集ocrmypdf的数据文件和子模块
ocrmypdf_datas = collect_data_files('ocrmypdf')
ocrmypdf_hiddenimports = collect_submodules('ocrmypdf')

# 收集pikepdf的数据文件（ocrmypdf的重要依赖）
pikepdf_datas = collect_data_files('pikepdf')
pikepdf_hiddenimports = collect_submodules('pikepdf')

a = Analysis(
    ['main.py'],  # 主程序入口
    pathex=['./'],  # 项目根目录
    binaries=[],
    datas=[
    ('utils/*', 'utils'),  # 包含自定义工具包
    ('routers/*', 'routers'),  # 添加路由目录
    ('schemas/*', 'schemas'),  # 添加模式目录
    ('core/*', 'core'),  # 保留核心目录
    ('requirements.txt', '.'),  # 添加依赖文件
    ('start.sh', '.'),  # 添加启动脚本
] + ocrmypdf_datas + pikepdf_datas,  # 添加ocrmypdf和pikepdf的数据文件
    hiddenimports=[
        # 项目模块
        'routers.file_translate',
        'routers.transcribe_audio',
        'routers.mail_parse',
        'schemas.file_translate',
        'schemas.transcribe',
        'utils.task',
        'utils.file',
        'utils.email_converter',
        'utils.transcribe.transcribe',
        'utils.translateion.translate',
        'utils.translateion.nllb',
        'utils.translateion.openai_translate',
        'utils.office_translate.word_v1',
        'utils.office_translate.excel_v1',
        'utils.office_translate.ppt_v1',
        'utils.office_translate.pdf_v1',
        'utils.office_translate.image',
        'utils.pdf2docx.pdf2docx_translate',
        'core.log',

        # Web框架
        'fastapi',
        'fastapi.staticfiles',
        'uvicorn',
        'starlette',
        'pydantic',

        # Windows COM组件
        'win32com.client',
        'pythoncom',

        # CLI框架
        'click',

        # Rich库组件
        'rich.console',
        'rich.progress',
        'rich.table',
        'rich.panel',
        'rich.text',

        # 数据处理
        'pandas',
        'openpyxl',
        'xlutils.copy',
        'xlrd',

        # 加密和安全
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.backends.openssl',

        # 系统和网络
        'psutil',
        'websockets',
        'httpx',

        # 日志和配置
        'loguru',
        'pyyaml',
        'pydantic',
        'pydantic_settings',

        # AI和翻译
        'whisperx',
        'torch',
        'torchaudio',
        'transformers',
        'openai',
        'ollama',
        'pdf2zh_next',

        # 文档处理
        'beautifulsoup4',
        'docx',
        'pptx',
        'fitz',  # PyMuPDF

        # OCRmyPDF完整模块列表
        'ocrmypdf',
        'ocrmypdf.data',
        'ocrmypdf.api',
        'ocrmypdf.cli',
        'ocrmypdf.exceptions',
        'ocrmypdf.helpers',
        'ocrmypdf.languages',
        'ocrmypdf.optimize',
        'ocrmypdf.pdfa',
        'ocrmypdf.quality',
        'ocrmypdf.imageops',
        'ocrmypdf.pluginspec',
        'ocrmypdf._validation',
        'ocrmypdf._pipeline',
        'ocrmypdf._metadata',
        'ocrmypdf._logging',
        'ocrmypdf._jobcontext',
        'ocrmypdf._graft',
        'ocrmypdf._defaults',
        'ocrmypdf._concurrent',
        'ocrmypdf._annots',
        'ocrmypdf._version',
        'ocrmypdf._plugin_manager',
        'ocrmypdf._progressbar',
        'ocrmypdf.__main__',

        # OCRmyPDF子包
        'ocrmypdf.builtin_plugins',
        'ocrmypdf.builtin_plugins.concurrency',
        'ocrmypdf.builtin_plugins.default_filters',
        'ocrmypdf.builtin_plugins.ghostscript',
        'ocrmypdf.builtin_plugins.optimize',
        'ocrmypdf.builtin_plugins.tesseract_ocr',
        'ocrmypdf.extra_plugins',
        'ocrmypdf.extra_plugins.semfree',
        'ocrmypdf.hocrtransform',
        'ocrmypdf.hocrtransform.__main__',
        'ocrmypdf.hocrtransform._font',
        'ocrmypdf.hocrtransform._hocr',
        'ocrmypdf.pdfinfo',
        'ocrmypdf.pdfinfo.info',
        'ocrmypdf.pdfinfo.layout',
        'ocrmypdf.subprocess',
        'ocrmypdf.subprocess._windows',
        'ocrmypdf._exec',
        'ocrmypdf._exec.ghostscript',
        'ocrmypdf._exec.jbig2enc',
        'ocrmypdf._exec.pngquant',
        'ocrmypdf._exec.tesseract',
        'ocrmypdf._exec.unpaper',
        'ocrmypdf._pipelines',
        'ocrmypdf._pipelines._common',
        'ocrmypdf._pipelines.hocr_to_ocr_pdf',
        'ocrmypdf._pipelines.ocr',
        'ocrmypdf._pipelines.pdf_to_hocr',

        # 压缩和加密检测
        'msoffcrypto',
        'rarfile',
        'py7zr',

        # 其他工具
        'tenacity',

        # OCRmyPDF依赖的其他模块
        'pikepdf',
        'pikepdf._qpdf',
        'pikepdf._core',
        'PIL',
        'PIL.Image',
        'reportlab',
        'reportlab.pdfgen',
        'reportlab.lib',
    ] + ocrmypdf_hiddenimports + pikepdf_hiddenimports,  # 添加自动收集的隐藏导入
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        # 移除numpy排除，因为其他库可能需要它
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name=exe_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=['vcruntime140.dll', 'python3.dll', 'python312.dll'] if sys.platform.startswith('win') else [],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,      # 可以添加图标文件路径
)
