from typing import Union, List
import logging
import time
import threading
import torch
from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
import pycld2
from pdf2zh_next.translator import BaseTranslator
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential, before_sleep_log


# 延迟导入重型依赖
def _lazy_import_torch():
    """延迟导入torch"""
    import torch
    return torch

def _lazy_import_transformers():
    """延迟导入transformers"""
    from transformers import AutoTokenizer, AutoModelForSeq2SeqLM
    return AutoTokenizer, AutoModelForSeq2SeqLM

def _lazy_import_pycld2():
    """延迟导入pycld2"""
    import pycld2
    return pycld2

def _lazy_import_pdf2zh():
    """延迟导入pdf2zh相关模块"""
    from pdf2zh_next import SettingsModel
    from pdf2zh_next.translator import BaseRateLimiter
    from pdf2zh_next.translator.base_translator import BaseTranslator
    return SettingsModel, BaseRateLimiter, BaseTranslator

def _lazy_import_tenacity():
    """延迟导入tenacity"""
    from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential, before_sleep_log
    return retry, retry_if_exception_type, stop_after_attempt, wait_exponential, before_sleep_log

# 设置日志
from core.log import setup_logger
logger = setup_logger(__name__)
# 并发控制
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import RLock, Semaphore

# 全局并发控制
_model_access_lock = RLock()  # 可重入锁，用于模型访问
_concurrent_semaphore = Semaphore(3)  # 最多3个并发翻译任务
# pycld2 语言代码到 NLLB 语言代码的映射
pycld2_to_nllb = {
    # 欧洲语系
    'en': 'eng_Latn',  # 英语
    'fr': 'fra_Latn',  # 法语
    'es': 'spa_Latn',  # 西班牙语
    'pt': 'por_Latn',  # 葡萄牙语
    'de': 'deu_Latn',  # 德语
    'it': 'ita_Latn',  # 意大利语
    'nl': 'nld_Latn',  # 荷兰语
    'pl': 'pol_Latn',  # 波兰语
    'ru': 'rus_Cyrl',  # 俄语
    'uk': 'ukr_Cyrl',  # 乌克兰语
    'bg': 'bul_Cyrl',  # 保加利亚语
    'cs': 'ces_Latn',  # 捷克语
    'da': 'dan_Latn',  # 丹麦语
    'el': 'ell_Grek',  # 希腊语
    'fi': 'fin_Latn',  # 芬兰语
    'hu': 'hun_Latn',  # 匈牙利语
    'ro': 'ron_Latn',  # 罗马尼亚语
    'sv': 'swe_Latn',  # 瑞典语

    # 亚洲语系
    'zh': 'zho_Hans',  # 简体中文
    'zh-Hant': 'zho_Hant',  # 繁体中文
    'ja': 'jpn_Jpan',  # 日语
    'ko': 'kor_Hang',  # 韩语
    'th': 'tha_Thai',  # 泰语
    'vi': 'vie_Latn',  # 越南语
    'id': 'ind_Latn',  # 印尼语
    'ms': 'msa_Latn',  # 马来语
    'hi': 'hin_Deva',  # 印地语
    'bn': 'ben_Beng',  # 孟加拉语
    'ta': 'tam_Taml',  # 泰米尔语
    'te': 'tel_Telu',  # 泰卢固语
    'ur': 'urd_Arab',  # 乌尔都语
    'fa': 'fas_Arab',  # 波斯语

    # 阿拉伯语系
    'ar': 'arb_Arab',  # 阿拉伯语

    # 非洲语系
    'sw': 'swh_Latn',  # 斯瓦希里语
    'am': 'amh_Ethi',  # 阿姆哈拉语
    'ha': 'hau_Latn',  # 豪萨语

    # 其他语系
    'he': 'heb_Hebr',  # 希伯来语
    'tr': 'tur_Latn',  # 土耳其语
    'ka': 'kat_Geor',  # 格鲁吉亚语
    'my': 'mya_Mymr',  # 缅甸语
    'km': 'khm_Khmr',  # 柬埔寨语
    'lo': 'lao_Laoo',  # 老挝语
    'si': 'sin_Sinh',  # 僧伽罗语
}

def _get_device():
    """延迟获取设备信息"""
    # torch = _lazy_import_torch()
    return "cuda" if torch.cuda.is_available() else "cpu"

model_name = "./models/nllb-200-600m"


# 使用单例模式管理模型
class NLLBModel:
    _instance = None
    _initialized = False
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:  # 双重检查锁定
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self.tokenizer = None
                    self.model = None
                    self._device = None
                    self._load_time = None
                    self._initialized = True

    def load(self):
        """延迟加载模型，支持设备选择和性能监控"""
        with self._lock:
            if self.tokenizer is None:
                logger.info("正在加载 NLLB tokenizer...")
                start_time = time.time()
                # AutoTokenizer, AutoModelForSeq2SeqLM = _lazy_import_transformers()
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                logger.info(f"Tokenizer 加载完成，耗时: {time.time() - start_time:.2f}秒")

            if self.model is None:
                device = _get_device()
                logger.info(f"正在加载 NLLB 模型到 {device}...")
                start_time = time.time()

                # 获取torch和transformers
                # torch = _lazy_import_torch()
                # AutoTokenizer, AutoModelForSeq2SeqLM = _lazy_import_transformers()

                # 根据设备选择合适的数据类型
                torch_dtype = torch.float16 if device == "cuda" else torch.float32
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_name,
                    torch_dtype=torch_dtype
                ).to(device)
                self._device = device
                self._load_time = time.time() - start_time
                logger.info(f"模型加载完成，耗时: {self._load_time:.2f}秒")

                # 记录内存使用情况
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                    logger.info(f"GPU内存使用: {memory_allocated:.2f}GB")

    def unload(self):
        """卸载模型以释放内存"""
        with self._lock:
            if self.model is not None:
                logger.info("正在卸载 NLLB 模型...")
                del self.model
                self.model = None
                self._device = None
                self._load_time = None

            if self.tokenizer is not None:
                logger.info("正在卸载 NLLB tokenizer...")
                del self.tokenizer
                self.tokenizer = None

            # 清理GPU缓存
            try:
                # torch = _lazy_import_torch()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                    logger.info(f"模型卸载完成，当前GPU内存使用: {memory_allocated:.2f}GB")
            except:
                logger.info("模型卸载完成")

    @property
    def is_loaded(self):
        """线程安全的加载状态检查"""
        with self._lock:
            return self.model is not None and self.tokenizer is not None

    @property
    def load_info(self):
        """获取模型加载信息"""
        with self._lock:
            return {
                'is_loaded': self.is_loaded,
                'device': self._device,
                'load_time': self._load_time,
                'model_name': model_name
            }

nllbModel = NLLBModel()
nllbModel.load()
# 延迟初始化全局实例，避免模块加载时立即加载模型
_nllb_instance = None
_nllb_lock = threading.Lock()

def get_nllb_model():
    """获取NLLB模型实例（延迟初始化）"""
    global _nllb_instance
    if _nllb_instance is None:
        with _nllb_lock:
            if _nllb_instance is None:
                _nllb_instance = NLLBModel()
    return _nllb_instance


class AdaptiveBatchProcessor:
    """自适应批处理管理器"""

    def __init__(self, initial_batch_size=8, min_batch_size=1, max_batch_size=32):
        self.current_batch_size = initial_batch_size
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.memory_threshold = 0.8  # 80% GPU 内存使用阈值
        self.performance_history = []

    def estimate_memory_usage(self, texts):
        """估算批次的内存使用量"""
        if not texts:
            return 0
        total_tokens = sum(len(text.split()) for text in texts)
        # 粗略估算：每个token约需要4字节（float32）+ 模型参数开销
        estimated_mb = (total_tokens * 4 * len(texts)) / (1024 * 1024)
        return estimated_mb

    def get_optimal_batch_size(self, texts):
        """根据文本长度和GPU内存动态调整批处理大小"""
        if not texts:
            return self.min_batch_size

        try:
            # torch = _lazy_import_torch()
            if not torch.cuda.is_available():
                return min(len(texts), self.current_batch_size)

            # 获取GPU内存信息
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)  # GB
            used_memory = torch.cuda.memory_allocated(0) / (1024**3)
            available_memory = gpu_memory - used_memory

            # 根据文本长度调整
            avg_length = sum(len(text) for text in texts) / len(texts)

            if avg_length > 200:  # 长文本
                adjusted_size = max(self.min_batch_size, self.current_batch_size // 2)
            elif avg_length < 50:  # 短文本
                adjusted_size = min(self.max_batch_size, self.current_batch_size * 2)
            else:
                adjusted_size = self.current_batch_size

            # 根据可用内存进一步调整
            if available_memory < 1.0:  # 少于1GB可用内存
                adjusted_size = max(self.min_batch_size, adjusted_size // 2)
            elif available_memory > 3.0:  # 大于3GB可用内存
                adjusted_size = min(self.max_batch_size, adjusted_size * 1.5)

            return min(len(texts), int(adjusted_size))

        except Exception as e:
            logger.warning(f"批处理大小调整失败，使用默认值: {e}")
            return min(len(texts), self.current_batch_size)

    def create_smart_batches(self, texts):
        """创建智能批次"""
        if not texts:
            return []

        batches = []
        remaining_texts = texts.copy()

        while remaining_texts:
            batch_size = self.get_optimal_batch_size(remaining_texts)
            batch = remaining_texts[:batch_size]
            batches.append(batch)
            remaining_texts = remaining_texts[batch_size:]

        return batches

    def update_performance(self, batch_size, processing_time, success):
        """更新性能历史"""
        self.performance_history.append({
            'batch_size': batch_size,
            'processing_time': processing_time,
            'success': success,
            'throughput': batch_size / processing_time if processing_time > 0 else 0
        })

        # 保持历史记录在合理范围内
        if len(self.performance_history) > 50:
            self.performance_history = self.performance_history[-50:]

        # 根据性能历史调整当前批处理大小
        if len(self.performance_history) >= 5:
            recent_performance = self.performance_history[-5:]
            avg_throughput = sum(p['throughput'] for p in recent_performance) / len(recent_performance)
            success_rate = sum(1 for p in recent_performance if p['success']) / len(recent_performance)

            if success_rate > 0.8 and avg_throughput > 0:
                # 性能良好，可以尝试增加批处理大小
                self.current_batch_size = min(self.max_batch_size,
                                            int(self.current_batch_size * 1.1))
            elif success_rate < 0.6:
                # 成功率低，减少批处理大小
                self.current_batch_size = max(self.min_batch_size,
                                            int(self.current_batch_size * 0.8))


class SmartCudaMemoryManager:
    """智能CUDA内存管理器"""

    def __init__(self, memory_threshold=0.85, cleanup_interval=3, enable_half_precision=True):
        self.memory_threshold = memory_threshold  # 内存使用阈值
        self.cleanup_interval = cleanup_interval  # 清理间隔（批次数）
        self.enable_half_precision = enable_half_precision
        self.batch_count = 0
        self.initial_memory = None
        self.peak_memory = 0
        self.cleanup_count = 0

    def __enter__(self):
        try:
            # torch = _lazy_import_torch()
            if torch.cuda.is_available():
                self.initial_memory = torch.cuda.memory_allocated()
                self.peak_memory = self.initial_memory
        except:
            self.initial_memory = 0
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            # torch = _lazy_import_torch()
            if torch.cuda.is_available():
                self.batch_count += 1
                current_memory = torch.cuda.memory_allocated()
                self.peak_memory = max(self.peak_memory, current_memory)

                total_memory = torch.cuda.get_device_properties(0).total_memory
                memory_usage = current_memory / total_memory

                # 智能清理策略
                should_cleanup = (
                    memory_usage > self.memory_threshold or
                    self.batch_count % self.cleanup_interval == 0 or
                    exc_type is not None  # 发生异常时强制清理
                )

                if should_cleanup:
                    torch.cuda.empty_cache()
                    self.cleanup_count += 1
                    after_cleanup_memory = torch.cuda.memory_allocated()
                    freed_memory = (current_memory - after_cleanup_memory) / (1024**2)  # MB

                    if freed_memory > 10:  # 只记录显著的内存释放
                        logger.debug(f"GPU内存清理: 释放 {freed_memory:.1f}MB, "
                                   f"使用率 {memory_usage:.1%} -> {after_cleanup_memory/total_memory:.1%}")
        except Exception as e:
            logger.warning(f"GPU内存管理失败: {e}")

    def get_memory_stats(self):
        """获取内存使用统计"""
        try:
            # torch = _lazy_import_torch()
            if torch.cuda.is_available():
                current = torch.cuda.memory_allocated()
                total = torch.cuda.get_device_properties(0).total_memory
                return {
                    'current_mb': current / (1024**2),
                    'peak_mb': self.peak_memory / (1024**2),
                    'total_mb': total / (1024**2),
                    'usage_percent': current / total * 100,
                    'cleanup_count': self.cleanup_count
                }
        except:
            pass
        return None


class MemoryEfficientNLLBModel(NLLBModel):
    """内存优化的NLLB模型"""

    def __init__(self):
        super().__init__()
        self.half_precision_enabled = False

    def load(self):
        """加载模型并启用内存优化"""
        with self._lock:
            if self.tokenizer is None:
                logger.info("正在加载 NLLB tokenizer...")
                start_time = time.time()
                # AutoTokenizer, AutoModelForSeq2SeqLM = _lazy_import_transformers()
                self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                logger.info(f"Tokenizer 加载完成，耗时: {time.time() - start_time:.2f}秒")

            if self.model is None:
                device = _get_device()
                logger.info(f"正在加载 NLLB 模型到 {device}...")
                start_time = time.time()

                # 获取torch和transformers
                # torch = _lazy_import_torch()
                # AutoTokenizer, AutoModelForSeq2SeqLM = _lazy_import_transformers()

                # 根据设备选择合适的数据类型
                torch_dtype = torch.float16 if device == "cuda" else torch.float32
                self.model = AutoModelForSeq2SeqLM.from_pretrained(
                    model_name,
                    torch_dtype=torch_dtype
                ).to(device)

                # 启用半精度模式以节省内存
                if device == "cuda" and torch_dtype == torch.float16:
                    self.half_precision_enabled = True
                    logger.info("已启用半精度模式以节省GPU内存")

                self._device = device
                self._load_time = time.time() - start_time
                logger.info(f"模型加载完成，耗时: {self._load_time:.2f}秒")

                # 记录内存使用情况
                if torch.cuda.is_available():
                    memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                    memory_reserved = torch.cuda.memory_reserved() / (1024**3)
                    logger.info(f"GPU内存使用: {memory_allocated:.2f}GB (已分配) / {memory_reserved:.2f}GB (已保留)")


# 创建内存优化的全局实例
_nllb_instance = None
_nllb_lock = threading.Lock()

def get_nllb_model():
    """获取内存优化的NLLB模型实例（延迟初始化）"""
    global _nllb_instance
    if _nllb_instance is None:
        with _nllb_lock:
            if _nllb_instance is None:
                _nllb_instance = MemoryEfficientNLLBModel()
    return _nllb_instance


class CudaMemoryManager:
    """向后兼容的CUDA内存管理器"""

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        try:
            # torch = _lazy_import_torch()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        except:
            pass  # 忽略导入错误

class Nllb200Translation:
    name = "nllb200"

    def __init__(
            self,
            settings=None,
            rate_limiter=None,
            lang_in:str = "eng_Latn",
            lang_out:str = "zho_Hans",
            llm_api: str = '',
            llm_api_key: str = '',
            llm_model: str = '',
            thread: int = 1,
            ignore_cache: bool = False,
            prompt: str = '',
    ):
        # 延迟导入基类
        if settings is not None and rate_limiter is not None:
            # SettingsModel, BaseRateLimiter, BaseTranslator = _lazy_import_pdf2zh()
            super().__init__(settings, rate_limiter)
        # super().__init__(settings, rate_limiter)
        self.lang_in = lang_in
        self.lang_out = lang_out
        # 使用延迟加载机制
        self._nllb_model = get_nllb_model()
        # 初始化自适应批处理器
        self.batch_processor = AdaptiveBatchProcessor()
        # 初始化智能内存管理器
        self.memory_manager = SmartCudaMemoryManager()

    @property
    def device(self):
        """延迟获取设备信息"""
        return _get_device()

    @property
    def tokenizer(self):
        """延迟获取tokenizer"""
        if not self._nllb_model.is_loaded:
            self._nllb_model.load()
        return self._nllb_model.tokenizer

    @property
    def model(self):
        """延迟获取model"""
        if not self._nllb_model.is_loaded:
            self._nllb_model.load()
        return self._nllb_model.model

    def get_memory_stats(self):
        """获取内存使用统计"""
        return self.memory_manager.get_memory_stats()

    def optimize_for_memory(self):
        """优化内存使用"""
        if hasattr(self._nllb_model, 'half_precision_enabled') and self._nllb_model.half_precision_enabled:
            logger.info("模型已启用半精度模式")

        # 调整批处理器设置以节省内存
        self.batch_processor.max_batch_size = min(16, self.batch_processor.max_batch_size)
        self.batch_processor.memory_threshold = 0.75  # 更保守的内存阈值

        logger.info("已应用内存优化设置")

    def estimate_memory_for_batch(self, texts):
        """估算批次所需内存"""
        if not texts:
            return 0

        # 估算输入张量内存
        total_chars = sum(len(text) for text in texts)
        estimated_tokens = total_chars // 4  # 粗略估算

        # 考虑模型精度
        bytes_per_param = 2 if hasattr(self._nllb_model, 'half_precision_enabled') and self._nllb_model.half_precision_enabled else 4

        # 估算内存使用（输入 + 输出 + 中间计算）
        estimated_mb = (estimated_tokens * len(texts) * bytes_per_param * 3) / (1024 * 1024)
        return estimated_mb


class ConcurrentNLLBTranslator(Nllb200Translation, BaseTranslator):
    """支持并发的NLLB翻译器"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.max_concurrent_batches = 2  # 最大并发批次数
        self.batch_semaphore = Semaphore(self.max_concurrent_batches)
        self.request_queue = queue.PriorityQueue()
        self.concurrent_stats = {
            'total_requests': 0,
            'concurrent_requests': 0,
            'max_concurrent': 0,
            'avg_wait_time': 0
        }

    def do_translate_concurrent(self, text, rate_limit_params=None):
        """支持并发的翻译方法"""
        is_single = isinstance(text, str)
        texts = [text] if is_single else text

        if len(texts) <= 8:  # 小批次直接处理
            return self._translate_single_batch(texts, is_single)

        # 大批次使用并发处理
        return self._translate_concurrent_batches(texts, is_single)

    def _translate_concurrent_batches(self, texts, is_single):
        """并发处理多个批次"""
        # 创建批次
        batches = self.batch_processor.create_smart_batches(texts)
        logger.info(f"并发处理 {len(batches)} 个批次")

        if len(batches) <= 1:
            # 单批次直接处理
            return self._translate_single_batch(texts, is_single)

        results = [None] * len(batches)

        # 使用线程池并发处理批次
        max_workers = min(len(batches), self.max_concurrent_batches)
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有批次任务
            future_to_index = {
                executor.submit(self._translate_single_batch, batch, False): i
                for i, batch in enumerate(batches)
            }

            # 收集结果
            for future in as_completed(future_to_index):
                batch_index = future_to_index[future]
                try:
                    batch_result = future.result()
                    results[batch_index] = batch_result
                except Exception as e:
                    logger.error(f"并发批次 {batch_index} 翻译失败: {e}")
                    results[batch_index] = batches[batch_index]  # 返回原文

        # 合并结果
        final_results = []
        for batch_result in results:
            if batch_result:
                if isinstance(batch_result, list):
                    final_results.extend(batch_result)
                else:
                    final_results.append(batch_result)

        return final_results[0] if is_single else final_results

    def _translate_single_batch(self, texts, is_single):
        """翻译单个批次（线程安全）"""
        request_start_time = time.time()

        # 使用信号量限制并发数
        with self.batch_semaphore:
            wait_time = time.time() - request_start_time

            # 更新并发统计
            self.concurrent_stats['total_requests'] += 1
            self.concurrent_stats['avg_wait_time'] = (
                (self.concurrent_stats['avg_wait_time'] * (self.concurrent_stats['total_requests'] - 1) + wait_time)
                / self.concurrent_stats['total_requests']
            )

            try:
                # 使用可重入锁保护模型访问
                with _model_access_lock:
                    # 确保模型已加载
                    if not self._nllb_model.is_loaded:
                        self._nllb_model.load()

                # 在锁外进行实际翻译以提高并发性
                return self._do_actual_translation(texts, is_single)

            except Exception as e:
                logger.error(f"批次翻译失败: {e}")
                return texts[0] if is_single else texts

    def _do_actual_translation(self, texts, is_single):
        """执行实际翻译（优化并发性能）"""
        # 语言检测
        src_lang = self.lang_in
        if src_lang.lower() == "auto" and texts:
            src_lang = self.detect_language(texts[0])

        # 设置源语言（线程安全）
        with _model_access_lock:
            self.tokenizer.src_lang = src_lang

        # 执行翻译
        with self.memory_manager:
            try:
                inputs = self.tokenizer(
                    texts,
                    return_tensors="pt",
                    padding=True,
                    truncation=True,
                    max_length=512
                )
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

                # torch = _lazy_import_torch()
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        forced_bos_token_id=self.tokenizer.convert_tokens_to_ids(self.lang_out),
                        max_length=512,
                        num_beams=5,
                        early_stopping=True
                    )

                translations = self.tokenizer.batch_decode(outputs, skip_special_tokens=True)

                # 清理张量
                del inputs, outputs

                return translations[0] if is_single else translations

            except Exception as e:
                logger.error(f"翻译执行失败: {e}")
                return texts[0] if is_single else texts

    def get_concurrent_stats(self):
        """获取并发统计信息"""
        return self.concurrent_stats.copy()

    def detect_language(self, text):
        """使用 pycld2 检测文本语言"""
        if not text or len(text.strip()) < 5:
            return "eng_Latn"  # 默认返回英语

        try:
            # 延迟导入pycld2
            # pycld2 = _lazy_import_pycld2()

            # 预处理文本
            processed_text = text.replace('\n', ' ').strip()
            # 获取预测结果
            # reliable: 检测结果是否可靠
            # text_bytes_found: 实际处理的文本字节数
            # details: 包含语言检测的详细信息，是一个元组列表

            reliable, _, details = pycld2.detect(processed_text, bestEffort=True)

            # details[0] 包含最可能的语言信息
            # 格式: (语言名称, 语言代码, 可信度百分比, 是否可靠)
            lang_code = details[0][1]
            confidence = details[0][2]

            logger.debug(f"pycld2 检测结果: 语言={lang_code}, 可信度={confidence}%, 可靠性={reliable}")

            # 转换为 NLLB 语言代码
            nllb_code = pycld2_to_nllb.get(lang_code, None)
            if nllb_code:
                logger.info(f"检测到语言: {lang_code} -> {nllb_code} (可信度: {confidence}%)")
                return nllb_code
            else:
                logger.warning(f"无法将 pycld2 语言代码 {lang_code} 映射到 NLLB 语言代码")
                return "eng_Latn"  # 默认返回英语

        except Exception as e:
            logger.error(f"语言检测失败: {str(e)}")
            return "eng_Latn"  # 默认返回英语
    def do_translate(self, text: Union[str, List[str]], rate_limit_params: dict = None)-> Union[str, List[str]]:
        """翻译方法，使用延迟导入的重试装饰器"""
        # 延迟导入tenacity
        # retry, retry_if_exception_type, stop_after_attempt, wait_exponential, before_sleep_log = _lazy_import_tenacity()

        # 创建重试装饰器
        retry_decorator = retry(
            retry=retry_if_exception_type(Exception),
            stop=stop_after_attempt(5),
            wait=wait_exponential(multiplier=1, min=1, max=15),
            before_sleep=before_sleep_log(logger, logging.WARNING),
        )

        # 应用重试装饰器并执行翻译
        return retry_decorator(self._do_translate_impl)(text, rate_limit_params)

    def _do_translate_impl(self, text: Union[str, List[str]], rate_limit_params: dict = None)-> Union[str, List[str]]:
        is_single = isinstance(text, str)

        # 将单个文本转换为列表以统一处理
        if is_single:
            texts = [text]
        else:
            texts = text

        valid_indices = []
        valid_texts = []
        for i, t in enumerate(texts):
            if t:
                valid_indices.append(i)
                valid_texts.append(t)

        if not valid_texts:
            return "" if is_single else []
        try:
            # 使用信号量控制并发，而不是全局锁
            with _concurrent_semaphore:
                # 准备源语言和目标语言代码
                src_lang = self.lang_in
                # 当传入的是auto时，会自动检测语言
                if src_lang.lower() == "auto" and valid_texts:
                    # 使用第一个有效文本进行语言检测
                    src_lang = self.detect_language(valid_texts[0])
                    logger.info(f"自动检测语言结果: {src_lang}")

                self.tokenizer.src_lang = src_lang

                # 使用自适应批处理
                batches = self.batch_processor.create_smart_batches(valid_texts)
                # logger.info(f"创建了 {len(batches)} 个智能批次，批次大小: {[len(b) for b in batches]}")

                result = []
                # 使用智能内存管理器
                with self.memory_manager:
                    for batch_idx, batch in enumerate(batches):
                        batch_start_time = time.time()
                        batch_success = True

                        try:
                            # 检查内存使用情况
                            memory_stats = self.memory_manager.get_memory_stats()
                            if memory_stats and memory_stats['usage_percent'] > 90:
                                logger.warning(f"GPU内存使用率过高: {memory_stats['usage_percent']:.1f}%")

                            # 批量编码输入文本
                            inputs = self.tokenizer(batch, return_tensors="pt", padding=True, truncation=True,
                                                    max_length=512)
                            inputs = {k: v.to(self.device) for k, v in inputs.items()}
                            # torch = _lazy_import_torch()

                            with torch.no_grad():
                                outputs = self.model.generate(
                                    **inputs,
                                    forced_bos_token_id=self.tokenizer.convert_tokens_to_ids(self.lang_out),
                                    max_length=512,
                                    num_beams=5,
                                    early_stopping=True
                                )

                            # 解码翻译结果
                            translations = self.tokenizer.batch_decode(outputs, skip_special_tokens=True)
                            result.extend(translations)

                            # 立即清理中间张量以释放内存
                            del inputs, outputs

                        except torch.cuda.OutOfMemoryError as e:
                            logger.error(f"批次 {batch_idx} GPU内存不足: {e}")
                            # 强制清理内存并重试更小的批次
                            torch.cuda.empty_cache()
                            batch_success = False
                            result.extend(batch)  # 返回原文

                        except Exception as e:
                            logger.error(f"批次 {batch_idx} 翻译失败: {e}")
                            batch_success = False
                            result.extend(batch)

                        # 记录批次性能
                        batch_time = time.time() - batch_start_time
                        self.batch_processor.update_performance(len(batch), batch_time, batch_success)

                # 将结果放回原始位置
                results = [""] * len(texts) if not is_single else [""]
                for i, trans_idx in enumerate(valid_indices):
                    # 确保结果是有效的字符串
                    if result[i] is not None and isinstance(result[i], str):
                        results[trans_idx] = result[i]
                    else:
                        # 无效结果使用原文
                        results[trans_idx] = texts[trans_idx] if texts[trans_idx] else ""

                # 最后再次清理 CUDA 缓存
                try:
                    # torch = _lazy_import_torch()
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except:
                    pass
                res = results[0] if is_single else results
                return res

        except Exception as e:
            logger.error(f"NLLB翻译失败: {str(e)}")
            # 翻译失败时返回原文
            return text if is_single else texts