import json
import re
from datetime import datetime
from typing import Dict, List, Any, Union
import concurrent.futures
import httpx
from openai import OpenAI
from tenacity import (
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
    retry,
)
import openai
from utils.translation.translate import RateLimiter, SmartBatchProcessor, <PERSON>rror<PERSON><PERSON><PERSON>, EnhancedRetryManager, RecoveryStrategy
from core.log import setup_logger
logger = setup_logger(__name__)

class OpenaiTranslate:
    name = "openai"
    def __init__(self,
                 llm_api: str = '',
                 llm_api_key: str = '',
                 llm_model: str = '',
                 lang_in: str = '',
                 lang_out: str = '',
                 thread: int = 5,
                 ignore_cache: bool = False,
                 prompt: str = '',
                 ):
        self.client = OpenAI(
            api_key=llm_api_key,
            base_url=llm_api,
            http_client = httpx.Client(
                limits=httpx.Limits(
                    max_connections=None, max_keepalive_connections=None
                ),
                # 忽略ssl
                verify=False,
        ),
        )
        self.model = llm_model
        self.llm_prompt = prompt
        self.thread = thread
        # 初始化速率限制器
        self.rate_limiter = RateLimiter(thread)
        # 初始化智能批处理管理器
        self.batch_processor = SmartBatchProcessor()
        # 初始化错误处理管理器
        self.error_handler = ErrorHandler()
        # 初始化重试管理器
        self.retry_manager = EnhancedRetryManager(self.error_handler)
        # 创建智能重试装饰器
        self.smart_retry = self.retry_manager.create_retry_decorator()

    def translate_smart_batch(self, texts: List[str], context_hints: List[str] = None) -> List[str]:
        """使用智能批处理策略翻译文本列表"""
        if not texts:
            return []

        if len(texts) == 1:
            # 单个文本直接翻译
            return [self.translate_single(texts[0])]

        # logger.info(f"开始智能批处理翻译 {len(texts)} 个文本")

        # 创建文本项
        text_items = self.batch_processor.create_text_items(texts, context_hints)

        # 创建智能批次
        batches = self.batch_processor.create_smart_batches(text_items)

        # logger.info(f"创建了 {len(batches)} 个智能批次")

        # 处理所有批次
        all_results = [None] * len(texts)  # 预分配结果列表
        total_processing_time = 0
        successful_batches = 0

        for i, batch in enumerate(batches):
            # logger.debug(f"处理批次 {i+1}/{len(batches)}: {len(batch)} 个文本")

            # 处理批次
            batch_results, metrics = self.batch_processor.process_batch_with_retry(
                batch, self._translate_batch_texts
            )

            # 将结果放回原始位置
            for item, result in zip(batch, batch_results):
                all_results[item.original_index] = result

            total_processing_time += metrics.processing_time
            if metrics.success_rate > 0.5:
                successful_batches += 1

            # logger.debug(f"批次 {i+1} 完成: 成功率 {metrics.success_rate:.2f}, "
            #             f"用时 {metrics.processing_time:.2f}s")

        # 记录总体性能
        overall_success_rate = successful_batches / len(batches) if batches else 0
        # logger.info(f"智能批处理完成: 总用时 {total_processing_time:.2f}s, "
        #            f"成功率 {overall_success_rate:.2f}")

        # 打印性能摘要
        performance_summary = self.batch_processor.get_performance_summary()
        # logger.debug(f"批处理性能摘要: {performance_summary}")

        return all_results

    def _translate_batch_texts(self, texts: List[str]) -> List[str]:
        """翻译一批文本（内部方法），使用增强错误处理"""
        results = []
        failed_indices = []

        for i, text in enumerate(texts):
            try:
                context = {
                    'original_text': text,
                    'batch_index': i,
                    'batch_size': len(texts)
                }
                result = self.translate_single(text, context)
                results.append(result)
            except Exception as e:
                # 记录失败的索引
                failed_indices.append(i)
                error_info = self.error_handler.handle_error(e, {
                    'batch_index': i,
                    'batch_size': len(texts),
                    'text_preview': text[:50] + '...' if len(text) > 50 else text
                })

                # 根据恢复策略处理
                if error_info.recovery_strategy == RecoveryStrategy.USE_ORIGINAL:
                    results.append(text)
                elif error_info.recovery_strategy == RecoveryStrategy.SKIP:
                    results.append("")
                else:
                    results.append(text)  # 默认使用原文

                # logger.warning(f"批次中第 {i+1} 个文本翻译失败，使用恢复策略: {error_info.recovery_strategy.value}")

        # 如果有失败的文本，记录批次级别的错误
        if failed_indices:
            batch_error_context = {
                'failed_count': len(failed_indices),
                'total_count': len(texts),
                'failure_rate': len(failed_indices) / len(texts),
                'failed_indices': failed_indices
            }
            # logger.warning(f"批次部分失败: {len(failed_indices)}/{len(texts)} 个文本失败")

        return results

    def translate_single(self, text: str, context: Dict[str, Any] = None) -> str:
        """翻译单个文本，使用增强错误处理"""
        if not text or not text.strip():
            return text

        context = context or {'original_text': text}
        context['text_length'] = len(text)

        def _do_translate():
            # 应用速率限制
            self.rate_limiter.acquire()

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self.llm_prompt,
                    },
                    {
                        "role": "user",
                        "content": text,
                    },
                ],
                extra_body={
                    "chat_template_kwargs": {"enable_thinking": False},
                },
            )
            content = self._remove_cot_content(response.choices[0].message.content or "")

            # 简单的翻译质量检测
            # if not content or content.strip() == text.strip():
            #     logger.warning(f"翻译质量异常: 输出与输入相同")

            return content

        def _fallback_translate():
            """降级翻译策略"""
            # logger.info("使用降级翻译策略")
            # 这里可以集成其他翻译服务作为备用
            return text  # 暂时返回原文

        try:
            # 使用重试管理器执行翻译
            return self.retry_manager.execute_with_fallback(
                primary_func=lambda: self.smart_retry(_do_translate)(),
                fallback_func=_fallback_translate,
                context=context
            )
        except Exception as e:
            # 最终错误处理
            error_info = self.error_handler.handle_error(e, context)
            logger.error(f"翻译最终失败: {error_info.message}")
            return text  # 返回原文

    @retry(
        retry=retry_if_exception_type(openai.RateLimitError),
        stop=stop_after_attempt(100),
        wait=wait_exponential(multiplier=1, min=1, max=15),
        before_sleep=lambda retry_state: logger.warning(
            f"RateLimitError, retrying in {retry_state.next_action.sleep} seconds... "
            f"(Attempt {retry_state.attempt_number}/100)"
        ),
    )
    def translate(self, text: Union[str, List[str]], use_smart_batch: bool = True, context_hints: List[str] = None) -> list[Any] | list[None] | str | None | Any:
        """
        翻译文本

        Args:
            text: 要翻译的文本或文本列表
            use_smart_batch: 是否使用智能批处理（默认True）
            context_hints: 上下文提示列表，用于优化批处理策略
        """
        # 定义单个文本的翻译函数
        def translate_single(t):
            if not t:
                return t
            try:
                # 应用速率限制
                self.rate_limiter.acquire()

                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": self.llm_prompt,
                        },
                        {
                            "role": "user",
                            "content": t,
                        },
                    ],
                    extra_body={
                        "chat_template_kwargs": {"enable_thinking": False},
                    },
                )
                content = self._remove_cot_content(response.choices[0].message.content or "")

                return content
            except Exception as e:
                logger.error(f"翻译失败: {str(e)}")
                return t  # 翻译失败时返回原文

        if isinstance(text, list):
            if not text:
                return []

            # 根据参数选择处理方式
            if use_smart_batch and len(text) > 0:
                # logger.info("使用智能批处理模式")
                return self.translate_smart_batch(text, context_hints)
            else:
                # logger.info("使用传统并发模式")
                return self._translate_concurrent_legacy(text, translate_single)

        elif isinstance(text, str):
            # if (max_token := len(text) * 5) > self.options["num_predict"]:
            #     self.options["num_predict"] = max_token
            return translate_single(text)
        return None

    def _translate_concurrent_legacy(self, text: List[str], translate_single_func) -> List[str]:
        """传统的并发翻译方法（向后兼容）"""
        # 使用线程池并发执行翻译，正确使用limits作为并发数
        results = [] * len(text)  # 预分配结果列表
        max_workers = min(4, len(text))  # 避免创建过多线程

        # logger.info(f"开始传统批量翻译 {len(text)} 个文本，使用 {max_workers} 个并发线程，QPS限制: {tool_settings.tool_config.qps}")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建任务到索引的映射
            future_to_idx = {executor.submit(translate_single_func, t): i for i, t in enumerate(text)}

            # 收集结果，保持原始顺序
            for future in concurrent.futures.as_completed(future_to_idx):
                print(future.result())
                idx = future_to_idx[future]
                try:
                    results[idx] = future.result()
                except Exception as e:
                    logger.error(f"处理翻译结果时出错: {str(e)}")
                    results[idx] = text[idx]  # 出错时保留原文

        return results

    def get_translation_health_status(self) -> Dict[str, Any]:
        """获取翻译系统健康状态"""
        error_summary = self.error_handler.get_error_summary()
        batch_summary = self.batch_processor.get_performance_summary()

        # 计算健康分数 (0-100)
        health_score = 100

        # 错误率影响
        if error_summary['error_rate'] > 0.1:  # 10%
            health_score -= 30
        elif error_summary['error_rate'] > 0.05:  # 5%
            health_score -= 15

        # 批处理成功率影响
        if 'recent_avg_success_rate' in batch_summary:
            success_rate = batch_summary['recent_avg_success_rate']
            if success_rate < 0.8:
                health_score -= 20
            elif success_rate < 0.9:
                health_score -= 10

        # 确定健康状态
        if health_score >= 90:
            status = "excellent"
        elif health_score >= 70:
            status = "good"
        elif health_score >= 50:
            status = "fair"
        else:
            status = "poor"

        return {
            "health_score": max(0, health_score),
            "status": status,
            "error_summary": error_summary,
            "batch_summary": batch_summary,
            "recommendations": self._get_health_recommendations(error_summary, batch_summary)
        }

    def _get_health_recommendations(self, error_summary: Dict, batch_summary: Dict) -> List[str]:
        """获取健康改进建议"""
        recommendations = []

        if error_summary['error_rate'] > 0.1:
            recommendations.append("错误率过高，建议检查网络连接和API配置")

        if error_summary.get('errors_by_type', {}).get('rate_limit_error', 0) > 0:
            recommendations.append("存在速率限制错误，建议降低QPS设置")

        if error_summary.get('errors_by_type', {}).get('authentication_error', 0) > 0:
            recommendations.append("存在认证错误，请检查API密钥配置")

        if batch_summary.get('recent_avg_success_rate', 1.0) < 0.8:
            recommendations.append("批处理成功率较低，建议减小批次大小")

        if not recommendations:
            recommendations.append("系统运行良好，无需特别调整")

        return recommendations

    def export_health_report(self, output_file: str = None) -> str:
        """导出健康报告"""
        health_status = self.get_translation_health_status()

        if not output_file:
            output_file = f"translation_health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(health_status, f, ensure_ascii=False, indent=2)
            logger.info(f"健康报告已导出到: {output_file}")
            return output_file
        except Exception as e:
            # logger.error(f"导出健康报告失败: {str(e)}")
            return ""

    @staticmethod
    def _remove_cot_content(content: str) -> str:
        """Remove text content with the thought chain from the chat response

        :param content: Non-streaming text content
        :return: Text without a thought chain
        """
        con = re.sub(r"^<think>.+?</think>", "", content, count=1, flags=re.DOTALL)
        con = re.sub(r"</think>", "", con, count=1, flags=re.DOTALL)
        return con

    def do_translate(self, text, rate_limit_params: dict = None) -> str:
        return self.translate(text)
    def do_llm_translate(self, text, rate_limit_params: dict = None):
        return self.translate(text)