from pdf2zh_next import SettingsModel
from pdf2zh_next.translator import BaseRateLimiter
from pdf2zh_next.translator.base_translator import BaseTranslator

from utils.translation.nllb import ConcurrentNLLBTranslator
from utils.translation.openai_translate import OpenaiTranslate
def new_translator(
        service: str = '',
        llm_api: str = '',
        llm_api_key: str = '',
        llm_model: str = '',
        lang_in: str = '',
        lang_out: str = '',
        thread: int = 1,
        ignore_cache: bool = False,
        prompt: str = '',
) -> BaseTranslator:
    translate = None
    for translator in [OpenaiTranslate, ConcurrentNLLBTranslator]:
        if service == translator.name:
            translate = translator(
                llm_api= llm_api,
                llm_api_key= llm_api_key,
                llm_model= llm_model,
                lang_in= lang_in,
                lang_out= lang_out,
                thread= thread,
                ignore_cache = ignore_cache,
                prompt= prompt,
            )
    if not translate:
        raise ValueError("Unsupported translation service")

    return translate


__all__ = [
    "new_translator",
    "BaseTranslator"
]