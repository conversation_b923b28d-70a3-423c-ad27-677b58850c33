# from .converter import Converter
from pdf2docx import Converter
from string import Template
from typing import Dict, Any
import os
from utils.office_translate import word_translate_v1 

def translate_pdf_to_docx(
        file: str,
        output: str,
        service: str = '',
        llm_api: str = '',
        llm_api_key: str = '',
        llm_model: str = '',
        lang_in: str = '',
        lang_out: str = '',
        thread: int = 1,
        ignore_cache: bool = False,
        prompt: str = '',
        ocr_lang: str = "eng",
        is_ocr: bool = False,
        is_translate: bool = False,
):
    if not os.path.exists(file):
        raise Exception(f"找不到文件: {file}")

    try:
        to_docx_patn = os.path.join(output, os.path.basename(file)+".docx")
        converter = Converter(file)
        result = converter.convert(to_docx_patn, ocr=1 if is_ocr else 0, ocr_language=ocr_lang)
        if result is not None:
            raise Exception(f"转换失败: {result}")

        if not is_translate:
            return {
                "text": "",
                "file": to_docx_patn,
            }

        results = word_translate_v1(
                file=to_docx_patn,
                output=output,
                service=service,
                llm_api=llm_api,
                llm_api_key=llm_api_key,
                llm_model=llm_model,
                lang_in=lang_in,
                lang_out=lang_out,
                thread=thread,
                ignore_cache=ignore_cache,
                prompt=prompt,
            )
        os.remove(to_docx_patn)
        return results
    except Exception as e:
        raise Exception(f"转换失败: {e}")

