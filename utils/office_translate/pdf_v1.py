import asyncio
from functools import partial
from pathlib import Path
from typing import Literal, Dict

import ocrmypdf
from babeldoc.format.pdf.high_level import async_translate as babeldoc_translate
from pdf2zh_next.config.model import WatermarkOutputMode as PDF2ZHWatermarkMode
from babeldoc.format.pdf.translation_config import (
    WatermarkOutputMode as BabelDOCWatermarkMode,
)
from pdf2zh_next.translator import get_translator
from pdf2zh_next.translator.rate_limiter.qps_rate_limiter import QPSRateLimiter

from utils.translation import new_translator
from pdf2zh_next.high_level import _get_glossaries, BabeldocError, TranslationError, SubprocessError
from babeldoc.format.pdf.translation_config import TranslationConfig as BabelDOCConfig
from pdf2zh_next.config.model import SettingsModel
from collections.abc import AsyncGenerator
import os
import pdf2zh_next
from core.log import setup_logger
from utils.translation.nllb import ConcurrentNLLBTranslator

logger = setup_logger(__name__)


# logger = logging.getLogger(__name__)

class Pdf2zhTranslator:
    def __init__(self,
                 input_file: str,
                 output_path: str,
                 thread: int = 5,
                 llm_prompt: str = "",
                 api_key: str = "",
                 llm_model: str = "",
                 base_url: str = "",
                 service: str  = "openai",
                 lang_in: str = "auto",
                 lang_out: str = "zh",
                 ):
        # self.lang_in = lang_in
        self.service = service
        translate_engine_settings = None
        if service == "openai":
            print("Using OpenAI")
            translate_engine_settings = pdf2zh_next.OpenAISettings(
                openai_api_key=api_key,
                openai_base_url=base_url,
                openai_model=llm_model,
            )

        self.settings = pdf2zh_next.SettingsModel(
            basic=pdf2zh_next.BasicSettings(
                # debug=True,
                input_files={input_file}
            ),
            pdf=pdf2zh_next.PDFSettings(
                no_mono=False,
                no_dual=True,
                watermark_output_mode=pdf2zh_next.WatermarkOutputMode.NoWatermark,
                skip_scanned_detection=True,
                ocr_workaround=True
            ),
            translation=pdf2zh_next.TranslationSettings(
                qps=thread,
                no_auto_extract_glossary=True,
                custom_system_prompt=llm_prompt,
                output=output_path,
                ignore_cache=True,
            ),
            translate_engine_settings=translate_engine_settings ,
        )

    def __call__(self, *args, **kwargs):
        try:
            return asyncio.run(self.translate_async())
        except KeyboardInterrupt:
            logger.info("Translation interrupted by user (Ctrl+C)")
            return 1  # Return error count = 1 to indicate interruption
        except RuntimeError as e:
            # Handle the case where run() is called from a running event loop
            if "asyncio.run() cannot be called from a running event loop" in str(e):
                # 在已运行的事件循环中，创建任务并等待完成
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，我们需要特殊处理
                    # 创建一个future来获取结果
                    future = asyncio.Future()

                    async def run_and_set_result():
                        try:
                            result = await self.translate_async()
                            future.set_result(result)
                        except Exception as ex:
                            future.set_exception(ex)

                    asyncio.create_task(run_and_set_result())
                    # 在实际使用中，可能需要重新考虑这种处理方式
                    # 这里返回一个特殊值表示异步执行
                    return 0
                else:
                    try:
                        return loop.run_until_complete(
                            self.translate_async()
                        )
                    except KeyboardInterrupt:
                        logger.info("Translation interrupted by user (Ctrl+C) in event loop")
                        return 1  # Return error count = 1 to indicate interruption
            else:
                raise

    async def translate_async(self):
        input_files = self.settings.basic.input_files
        assert len(input_files) >= 1, "At least one input file is required"
        self.settings.basic.input_files = set()
        for file in input_files:
            async for event in self.do_translate_async_stream(self.settings, file):
                pass

    async def do_translate_async_stream(self,
                                        settings: SettingsModel, file: Path | str
                                        ) -> AsyncGenerator[dict, None]:
        # settings.validate_settings()
        if isinstance(file, str):
            file = Path(file)

        if settings.basic.input_files and len(settings.basic.input_files):
            logger.warning(
                "settings.basic.input_files is for cli & config, "
                "pdf2zh_next.highlevel.do_translate_async_stream will ignore this field "
                "and only translate the file pointed to by the file parameter."
            )

        if not file.exists():
            raise FileNotFoundError(f"file {file} not found")

        # 开始翻译
        # translate_func = partial(_translate_in_subprocess, settings, file)

        babeldoc_config = self.create_babeldoc_config(settings, file)
        logger.debug("debug mode, translate in main process")
        translate_func = partial(babeldoc_translate, translation_config=babeldoc_config)

        try:
            async for event in translate_func():
                yield event
                if settings.basic.debug:
                    logger.debug(event)
                if event["type"] == "finish":
                    break
        except TranslationError as e:
            # Log and re-raise structured errors
            logger.error(f"Translation error: {e}")
            if isinstance(e, BabeldocError) and e.original_error:
                logger.error(f"Original babeldoc error: {e.original_error}")
            elif isinstance(e, SubprocessError) and e.traceback_str:
                logger.error(f"Subprocess traceback: {e.traceback_str}")
            # Create an error event to yield to client code
            error_event = {
                "type": "error",
                "error": str(e) if not isinstance(e, SubprocessError) else e.raw_message,
                "error_type": e.__class__.__name__,
                "details": getattr(e, "original_error", "")
                           or getattr(e, "traceback_str", "")
                           or "",
            }
            yield error_event
            raise  # Re-raise the exception so that the caller can handle it if needed

    def create_babeldoc_config(self, settings: SettingsModel, file: Path) -> BabelDOCConfig:
        if not isinstance(settings, SettingsModel):
            raise ValueError(f"{type(settings)} is not SettingsModel")

        # translator = new_translator(
        #     settings = settings,
        #     rate_limiter=QPSRateLimiter(settings.translation.qps),
        # )
        translator = None
        if self.service == "openai":
            translator = get_translator(settings)
        else:
            translator = ConcurrentNLLBTranslator(settings=settings, rate_limiter=QPSRateLimiter(settings.translation.qps),lang_in="eng_Latn", lang_out="zho_Hans")
        if translator is None:
            raise ValueError("No translator found")

        # 设置分割策略
        split_strategy = None
        if settings.pdf.max_pages_per_part:
            split_strategy = BabelDOCConfig.create_max_pages_per_part_split_strategy(
                settings.pdf.max_pages_per_part
            )

        # 设置水印模式

        if settings.pdf.watermark_output_mode == PDF2ZHWatermarkMode.Both:
            watermark_mode = BabelDOCWatermarkMode.Both
        elif settings.pdf.watermark_output_mode == PDF2ZHWatermarkMode.NoWatermark:
            watermark_mode = BabelDOCWatermarkMode.NoWatermark
        else:
            watermark_mode = BabelDOCWatermarkMode.Watermarked

        table_model = None
        if settings.pdf.translate_table_text:
            from babeldoc.docvision.table_detection.rapidocr import RapidOCRModel

            table_model = RapidOCRModel()

        babeldoc_config = BabelDOCConfig(
            input_file=file,
            font=None,
            pages=settings.pdf.pages,
            output_dir=settings.translation.output,
            doc_layout_model=None,
            translator=translator,
            debug=settings.basic.debug,
            lang_in=settings.translation.lang_in,
            lang_out=settings.translation.lang_out,
            no_dual=settings.pdf.no_dual,
            no_mono=settings.pdf.no_mono,
            qps=settings.translation.qps,
            # 传递原来缺失的参数
            formular_font_pattern=settings.pdf.formular_font_pattern,
            formular_char_pattern=settings.pdf.formular_char_pattern,
            split_short_lines=settings.pdf.split_short_lines,
            short_line_split_factor=settings.pdf.short_line_split_factor,
            disable_rich_text_translate=settings.pdf.disable_rich_text_translate,
            dual_translate_first=settings.pdf.dual_translate_first,
            enhance_compatibility=settings.pdf.enhance_compatibility,
            use_alternating_pages_dual=settings.pdf.use_alternating_pages_dual,
            watermark_output_mode=watermark_mode,
            min_text_length=settings.translation.min_text_length,
            report_interval=settings.report_interval,
            skip_clean=settings.pdf.skip_clean,
            # 添加分割策略
            split_strategy=split_strategy,
            # 添加表格模型，仅在需要翻译表格时
            table_model=table_model,
            skip_scanned_detection=settings.pdf.skip_scanned_detection,
            ocr_workaround=settings.pdf.ocr_workaround,
            custom_system_prompt=settings.translation.custom_system_prompt,
            glossaries=_get_glossaries(settings),
            auto_enable_ocr_workaround=settings.pdf.auto_enable_ocr_workaround,
            pool_max_workers=settings.translation.pool_max_workers,
            auto_extract_glossary=not settings.translation.no_auto_extract_glossary,
            primary_font_family=settings.translation.primary_font_family,
            only_include_translated_page=settings.pdf.only_include_translated_page,
        )
        return babeldoc_config



def pdf_translate(
        file: str,
        output: str,
        service: str = '',
        llm_api: str = '',
        llm_api_key: str = '',
        llm_model: str = '',
        lang_in: str = '',
        lang_out: str = '',
        thread: int = 1,
        ignore_cache: bool = False,
        prompt: str = '',
        ocr_lang: str = False,
        is_ocr: bool = False,
        is_translate: bool = False
) -> Dict[str, str] | None:
    if not os.path.exists(file):
        raise Exception(f"找不到文件: {file}")
    if ocr_lang == "auto":
        language = None
    else:
        language = ocr_lang
    ocrFile = os.path.join(os.path.dirname(file), "ocr_" + os.path.basename(file))
    ocrRes = ocrmypdf.ocr(file, ocrFile,
                 progress_bar=False, skip_text=not is_ocr,
                 force_ocr=is_ocr, output_type="pdf", language=language)
    if ocrRes == 0:
        file = ocrFile

    pdf = Pdf2zhTranslator(
        input_file=file,
        output_path=output,
        service=service,
        base_url=llm_api,
        api_key=llm_api_key,
        llm_model=llm_model,
        lang_in=lang_in,
        lang_out=lang_out,
        thread=thread,
        llm_prompt=prompt,
    )
    pdf()
    # 提取文件name
    return {
        "text": "",
        "file": Path(os.path.basename(file)).stem + ".no_watermark.zh.mono.pdf"
    }
    # translator = new_translator(
    #    service = service,
    #    lang_in = lang_in,
    #    lang_out = lang_out,
    #    prompt = prompt,
    #    llm_api = llm_api,
    #    llm_api_key = llm_api_key,
    #    llm_model = llm_model,
    #    thread = thread,
    #     ignore_cache = ignore_cache,
    # )
    # doc_layout_model = DocLayoutModel.load_onnx()
    # watermark_output_mode = WatermarkOutputMode.NoWatermark
    # table_model = RapidOCRModel()
    # config = TranslationConfig(
    #             input_file=file,
    #             font=None,
    #             output_dir=output,
    #             translator=translator,
    #             lang_in=lang_in,
    #             lang_out=lang_out,
    #
    #             # no_dual=True,
    #             doc_layout_model=doc_layout_model,
    #             watermark_output_mode=watermark_output_mode,
    #             table_model=table_model,
    #             skip_scanned_detection=True,  # 跳过扫描文档检测
    #             ocr_workaround=True,  # 启用OCR优化
    #             # custom_system_prompt="你是一个标准的及其翻译引擎",  # 自定义系统提示
    #             # translate_table_text=True,  # 启用表格文本翻译
    #         )
    #
    # res = translate(config)
    # return {
    #     "text": "",
    #     "file": str(res.no_watermark_mono_pdf_path)
    # }
