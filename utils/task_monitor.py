"""
任务管理器监控和诊断工具
用于检测并发问题、死锁和性能瓶颈
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import psutil
import threading
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class TaskStats:
    """任务统计信息"""
    total_tasks: int
    queued_tasks: int
    running_tasks: int
    completed_tasks: int
    failed_tasks: int
    queue_size: int
    workers_running: bool
    active_files: int
    avg_processing_time: float
    memory_usage_mb: float
    cpu_usage_percent: float

@dataclass
class HealthReport:
    """健康状况报告"""
    status: HealthStatus
    score: float
    issues: List[str]
    recommendations: List[str]
    stats: TaskStats
    timestamp: datetime

class TaskMonitor:
    """任务管理器监控器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.task_history = []
        self.performance_metrics = []
        self.lock_wait_times = []
        self.is_monitoring = False
        self.monitor_task = None
        
    async def start_monitoring(self, interval: int = 30):
        """开始监控"""
        if self.is_monitoring:
            logger.warning("监控已在运行")
            return
            
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop(interval))
        logger.info(f"任务监控已启动，监控间隔: {interval}秒")
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("任务监控已停止")
    
    async def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.is_monitoring:
            try:
                await self._collect_metrics()
                await asyncio.sleep(interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环出错: {str(e)}", exc_info=True)
                await asyncio.sleep(interval)
    
    async def _collect_metrics(self):
        """收集性能指标"""
        from utils.task import TaskManager
        
        try:
            # 获取任务统计
            stats = await TaskManager.get_stats()
            
            # 获取系统资源使用情况
            process = psutil.Process()
            memory_info = process.memory_info()
            cpu_percent = process.cpu_percent()
            
            # 计算平均处理时间
            avg_time = self._calculate_avg_processing_time()
            
            task_stats = TaskStats(
                total_tasks=stats['total_tasks'],
                queued_tasks=stats['queued'],
                running_tasks=stats['running'],
                completed_tasks=stats['completed'],
                failed_tasks=stats['failed'],
                queue_size=stats['queue_size'],
                workers_running=stats['workers_running'],
                active_files=stats['active_files'],
                avg_processing_time=avg_time,
                memory_usage_mb=memory_info.rss / 1024 / 1024,
                cpu_usage_percent=cpu_percent
            )
            
            # 保存指标历史
            self.performance_metrics.append({
                'timestamp': datetime.now(),
                'stats': task_stats
            })
            
            # 保持最近100条记录
            if len(self.performance_metrics) > 100:
                self.performance_metrics = self.performance_metrics[-100:]
                
        except Exception as e:
            logger.error(f"收集指标失败: {str(e)}", exc_info=True)
    
    def _calculate_avg_processing_time(self) -> float:
        """计算平均处理时间"""
        if not self.task_history:
            return 0.0
        
        recent_tasks = [t for t in self.task_history if t.get('end_time')]
        if not recent_tasks:
            return 0.0
        
        total_time = sum(
            (t['end_time'] - t['start_time']).total_seconds() 
            for t in recent_tasks[-50:]  # 最近50个任务
        )
        return total_time / len(recent_tasks[-50:])
    
    async def get_health_report(self) -> HealthReport:
        """生成健康状况报告"""
        from utils.task import TaskManager
        
        stats = await TaskManager.get_stats()
        
        # 获取系统资源使用情况
        process = psutil.Process()
        memory_info = process.memory_info()
        cpu_percent = process.cpu_percent()
        avg_time = self._calculate_avg_processing_time()
        
        task_stats = TaskStats(
            total_tasks=stats['total_tasks'],
            queued_tasks=stats['queued'],
            running_tasks=stats['running'],
            completed_tasks=stats['completed'],
            failed_tasks=stats['failed'],
            queue_size=stats['queue_size'],
            workers_running=stats['workers_running'],
            active_files=stats['active_files'],
            avg_processing_time=avg_time,
            memory_usage_mb=memory_info.rss / 1024 / 1024,
            cpu_usage_percent=cpu_percent
        )
        
        # 分析健康状况
        issues = []
        recommendations = []
        health_score = 100.0
        
        # 检查队列积压
        if task_stats.queue_size > 50:
            issues.append(f"队列积压严重: {task_stats.queue_size} 个任务等待处理")
            recommendations.append("考虑增加工作协程数量")
            health_score -= 20
        elif task_stats.queue_size > 20:
            issues.append(f"队列积压: {task_stats.queue_size} 个任务等待处理")
            health_score -= 10
        
        # 检查失败率
        if task_stats.total_tasks > 0:
            failure_rate = task_stats.failed_tasks / task_stats.total_tasks
            if failure_rate > 0.1:
                issues.append(f"任务失败率过高: {failure_rate:.1%}")
                recommendations.append("检查任务执行逻辑和错误处理")
                health_score -= 30
            elif failure_rate > 0.05:
                issues.append(f"任务失败率偏高: {failure_rate:.1%}")
                health_score -= 15
        
        # 检查内存使用
        if task_stats.memory_usage_mb > 1000:
            issues.append(f"内存使用过高: {task_stats.memory_usage_mb:.1f}MB")
            recommendations.append("检查内存泄漏，考虑增加清理频率")
            health_score -= 25
        elif task_stats.memory_usage_mb > 500:
            issues.append(f"内存使用偏高: {task_stats.memory_usage_mb:.1f}MB")
            health_score -= 10
        
        # 检查CPU使用
        if task_stats.cpu_usage_percent > 80:
            issues.append(f"CPU使用率过高: {task_stats.cpu_usage_percent:.1f}%")
            recommendations.append("考虑优化任务处理逻辑或增加处理间隔")
            health_score -= 20
        
        # 检查平均处理时间
        if task_stats.avg_processing_time > 300:  # 5分钟
            issues.append(f"任务处理时间过长: {task_stats.avg_processing_time:.1f}秒")
            recommendations.append("优化任务处理逻辑，考虑分解大任务")
            health_score -= 15
        
        # 检查工作协程状态
        if not task_stats.workers_running and task_stats.queue_size > 0:
            issues.append("工作协程未运行但有任务等待处理")
            recommendations.append("重启工作协程")
            health_score -= 50
        
        # 确定健康状态
        if health_score >= 80:
            status = HealthStatus.HEALTHY
        elif health_score >= 60:
            status = HealthStatus.WARNING
        else:
            status = HealthStatus.CRITICAL
        
        return HealthReport(
            status=status,
            score=max(0, health_score),
            issues=issues,
            recommendations=recommendations,
            stats=task_stats,
            timestamp=datetime.now()
        )
    
    async def detect_deadlocks(self) -> List[str]:
        """检测潜在的死锁情况"""
        issues = []
        
        # 检查长时间运行的任务
        current_time = datetime.now()
        long_running_threshold = timedelta(minutes=30)
        
        from utils.task import TaskManager
        async with TaskManager._lock:
            for task_id, task_info in TaskManager._tasks.items():
                if task_info['status'] == 'running':
                    runtime = current_time - task_info['start_time']
                    if runtime > long_running_threshold:
                        issues.append(f"任务 {task_id} 运行时间过长: {runtime}")
        
        return issues
    
    def record_task_start(self, task_id: str):
        """记录任务开始"""
        self.task_history.append({
            'task_id': task_id,
            'start_time': datetime.now(),
            'end_time': None
        })
    
    def record_task_end(self, task_id: str):
        """记录任务结束"""
        for task in reversed(self.task_history):
            if task['task_id'] == task_id and not task['end_time']:
                task['end_time'] = datetime.now()
                break

# 全局监控实例
task_monitor = TaskMonitor()
