import logging
from typing import Dict, Any
import asyncio
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor
import threading
import weakref

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TaskManager:
    _tasks: Dict[str, Dict[str, Any]] = {}
    _executor = ThreadPoolExecutor(max_workers=4)  # 控制并发数量
    _lock = asyncio.Lock()  # 添加锁来保护共享资源
    _file_lock = asyncio.Lock()  # 文件操作专用锁
    _queue = asyncio.Queue()  # 任务队列
    _workers_running = False  # 标记工作协程是否在运行
    _workers_lock = asyncio.Lock()  # 保护 _workers_running 状态的锁
    _active_files = set()  # 跟踪正在使用的文件
    
    @classmethod
    async def create_task(cls, task_id: str, coro) -> None:
        """创建一个新的异步任务并加入队列"""
        logger.info(f"Creating new task with ID: {task_id}")
        async with cls._lock:
            cls._tasks[task_id] = {
                'status': 'queued',  # 初始状态为排队中
                'start_time': datetime.now(),
                'result': None,
                'error': None,
            }

        # 将任务添加到队列
        await cls._queue.put((task_id, coro))
        logger.debug(f"Task {task_id} added to queue")

        # 确保工作协程在运行 - 使用锁保护状态检查和修改
        async with cls._workers_lock:
            if not cls._workers_running:
                logger.info("Starting worker coroutines")
                cls._workers_running = True
    
    @classmethod
    async def _worker(cls):
        """工作协程，从队列中获取任务并执行"""
        logger.info("Worker started")
        while True:
            try:
                # 从队列获取任务
                task_id, coro = await cls._queue.get()

                # 更新任务状态为运行中
                async with cls._lock:
                    if task_id in cls._tasks:
                        cls._tasks[task_id]['status'] = 'running'

                logger.info(f"Processing task: {task_id}")

                try:
                    # 修复：直接执行协程，不创建新的事件循环
                    result = await cls._execute_task(coro)

                    # 更新任务状态为完成
                    async with cls._lock:
                        if task_id in cls._tasks:
                            cls._tasks[task_id]['status'] = 'completed'
                            cls._tasks[task_id]['result'] = result
                            if result and result.get('error') is not None:
                                cls._tasks[task_id]['status'] = 'failed'
                            logger.info(f"Task {task_id} completed successfully")

                except Exception as e:
                    logger.error(f"Task {task_id} failed with error: {str(e)}", exc_info=True)
                    # 更新任务状态为失败
                    async with cls._lock:
                        if task_id in cls._tasks:
                            cls._tasks[task_id]['status'] = 'failed'
                            cls._tasks[task_id]['error'] = str(e)

                finally:
                    # 标记队列任务完成
                    cls._queue.task_done()

            except Exception as e:
                logger.error(f"Worker error: {str(e)}", exc_info=True)
                await asyncio.sleep(1)  # 避免因错误导致的无限循环消耗资源
    
    @classmethod
    async def _execute_task(cls, coro):
        """执行实际的任务协程"""
        try:
            result = await coro
            # 确保返回标准格式的结果
            if isinstance(result, dict):
                return result
            else:
                return {"data": result}
        except Exception as e:
            logger.error(f"Task execution failed: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    @classmethod
    async def get_task_status(cls, task_id: str) -> Dict[str, Any]:
        logger.debug(f"Checking status for task: {task_id}")
        async with cls._lock:
            if task_id not in cls._tasks:
                logger.warning(f"Task {task_id} not found")
                return {'status': 'not_found'}
            
            task_info = cls._tasks[task_id].copy()
            if 'task' in task_info:
                task_info.pop('task')
            return task_info

    @classmethod
    async def clean_completed_tasks(cls):
        """清理已完成的任务 - 优化版本，减少锁持有时间"""
        logger.info("Starting cleanup of completed tasks")

        # 第一步：快速收集需要清理的任务ID，减少锁持有时间
        tasks_to_remove = []
        current_time = datetime.now()

        async with cls._lock:
            for task_id, task in cls._tasks.items():
                # 只清理已完成或失败的任务
                if task['status'] in ['completed', 'failed']:
                    time_diff = (current_time - task['start_time']).total_seconds()
                    if time_diff > 3600:  # 清理超过1小时的已完成任务
                        tasks_to_remove.append(task_id)

        # 第二步：批量删除任务，再次获取锁但时间很短
        if tasks_to_remove:
            async with cls._lock:
                initial_count = len(cls._tasks)
                for task_id in tasks_to_remove:
                    if task_id in cls._tasks:  # 双重检查，防止并发删除
                        cls._tasks.pop(task_id)
                        logger.debug(f"Removed task: {task_id}")
                final_count = len(cls._tasks)
                logger.info(f"Cleanup completed. Removed {initial_count - final_count} tasks")
        else:
            logger.info("No tasks to clean up")
    
    @classmethod
    async def clean_upload_files(cls):
        """清理upload中，10分钟之前的文件 - 线程安全版本"""
        async with cls._file_lock:  # 使用文件操作专用锁
            try:
                if not os.path.exists('upload'):
                    logger.info("Upload directory does not exist, skipping cleanup")
                    return

                now = datetime.now()
                # 修正：实际删除120分钟前的文件（注释说10分钟但代码是120分钟）
                cleanup_time_ago = now - timedelta(minutes=120)

                files_to_remove = []

                # 第一步：收集需要删除的文件，检查是否正在使用
                for filename in os.listdir('upload'):
                    file_path = os.path.join('upload', filename)

                    # 跳过正在使用的文件
                    if file_path in cls._active_files:
                        logger.debug(f"Skipping active file: {filename}")
                        continue

                    try:
                        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                        if file_time < cleanup_time_ago:
                            files_to_remove.append((filename, file_path))
                    except (OSError, ValueError) as e:
                        logger.warning(f"Could not get creation time for {filename}: {e}")

                # 第二步：安全删除文件
                removed_count = 0
                for filename, file_path in files_to_remove:
                    try:
                        # 再次检查文件是否仍然存在且未被使用
                        if os.path.exists(file_path) and file_path not in cls._active_files:
                            os.remove(file_path)
                            logger.info(f"Removed file: {filename}")
                            removed_count += 1
                    except OSError as e:
                        logger.warning(f"Could not remove file {filename}: {e}")

                logger.info(f"File cleanup completed. Removed {removed_count} files")

            except Exception as e:
                logger.error(f"Error during file cleanup: {str(e)}", exc_info=True)


    @classmethod
    async def start_workers(cls, num_workers: int = 2):
        """启动指定数量的工作协程"""
        async with cls._workers_lock:  # 使用锁保护状态检查和修改
            if cls._workers_running:
                logger.warning("Workers already running")
                return

            logger.info(f"Starting {num_workers} worker coroutines")
            cls._workers_running = True
            for _ in range(num_workers):
                asyncio.create_task(cls._worker())

    @classmethod
    async def register_active_file(cls, file_path: str):
        """注册正在使用的文件，防止被清理"""
        async with cls._file_lock:
            cls._active_files.add(file_path)
            logger.debug(f"Registered active file: {file_path}")

    @classmethod
    async def unregister_active_file(cls, file_path: str):
        """取消注册文件，允许被清理"""
        async with cls._file_lock:
            cls._active_files.discard(file_path)
            logger.debug(f"Unregistered active file: {file_path}")

    @classmethod
    async def get_stats(cls):
        """获取任务管理器统计信息"""
        async with cls._lock:
            stats = {
                'total_tasks': len(cls._tasks),
                'queued': sum(1 for t in cls._tasks.values() if t['status'] == 'queued'),
                'running': sum(1 for t in cls._tasks.values() if t['status'] == 'running'),
                'completed': sum(1 for t in cls._tasks.values() if t['status'] == 'completed'),
                'failed': sum(1 for t in cls._tasks.values() if t['status'] == 'failed'),
                'queue_size': cls._queue.qsize(),
                'workers_running': cls._workers_running,
                'active_files': len(cls._active_files)
            }
        return stats