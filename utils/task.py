import logging
from typing import Dict, Any
import asyncio
import os
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TaskManager:
    _tasks: Dict[str, Dict[str, Any]] = {}
    _executor = ThreadPoolExecutor(max_workers=4)  # 控制并发数量
    _lock = asyncio.Lock()  # 添加锁来保护共享资源
    _queue = asyncio.Queue()  # 任务队列
    _workers_running = False  # 标记工作协程是否在运行
    
    @classmethod
    async def create_task(cls, task_id: str, coro) -> None:
        """创建一个新的异步任务并加入队列"""
        logger.info(f"Creating new task with ID: {task_id}")
        async with cls._lock:
            cls._tasks[task_id] = {
                'status': 'queued',  # 初始状态为排队中
                'start_time': datetime.now(),
                'result': None,
                'error': None,
            }
        
        # 将任务添加到队列
        await cls._queue.put((task_id, coro))
        logger.debug(f"Task {task_id} added to queue")
        
        # 确保工作协程在运行
        if not cls._workers_running:
            logger.info("Starting worker coroutines")
            cls._workers_running = True
    
    @classmethod
    async def _worker(cls):
        """工作协程，从队列中获取任务并执行"""
        logger.info("Worker started")
        while True:
            try:
                # 从队列获取任务
                task_id, coro = await cls._queue.get()
                
                # 更新任务状态为运行中
                async with cls._lock:
                    if task_id in cls._tasks:
                        cls._tasks[task_id]['status'] = 'running'
                
                logger.info(f"Processing task: {task_id}")
                
                try:
                    # 使用线程池执行耗时操作
                    loop = asyncio.get_running_loop()
                    result = await loop.run_in_executor(
                        cls._executor,
                        lambda: asyncio.run(cls._execute_task(coro))
                    )
                    
                    # 更新任务状态为完成
                    async with cls._lock:
                        if task_id in cls._tasks:
                            cls._tasks[task_id]['status'] = 'completed'
                            cls._tasks[task_id]['result'] = result
                            if result.get('error') is not None:
                                cls._tasks[task_id]['status'] = 'failed'
                            logger.info(f"Task {task_id} completed successfully")
                
                except Exception as e:
                    logger.error(f"Task {task_id} failed with error: {str(e)}", exc_info=True)
                    # 更新任务状态为失败
                    async with cls._lock:
                        if task_id in cls._tasks:
                            cls._tasks[task_id]['status'] = 'failed'
                            cls._tasks[task_id]['error'] = str(e)
                
                finally:
                    # 标记队列任务完成
                    cls._queue.task_done()
            
            except Exception as e:
                logger.error(f"Worker error: {str(e)}", exc_info=True)
                await asyncio.sleep(1)  # 避免因错误导致的无限循环消耗资源
    
    @classmethod
    async def _execute_task(cls, coro):
        """执行实际的任务协程"""
        try:
            result = await coro
            # 确保返回标准格式的结果
            if isinstance(result, dict):
                return result
            else:
                return {"data": result}
        except Exception as e:
            logger.error(f"Task execution failed: {str(e)}", exc_info=True)
            return {"error": str(e)}
    
    @classmethod
    async def get_task_status(cls, task_id: str) -> Dict[str, Any]:
        logger.debug(f"Checking status for task: {task_id}")
        async with cls._lock:
            if task_id not in cls._tasks:
                logger.warning(f"Task {task_id} not found")
                return {'status': 'not_found'}
            
            task_info = cls._tasks[task_id].copy()
            if 'task' in task_info:
                task_info.pop('task')
            return task_info

    @classmethod
    async def clean_completed_tasks(cls):
        """清理已完成的任务"""
        logger.info("Starting cleanup of completed tasks")
        async with cls._lock:
            initial_count = len(cls._tasks)
            for task_id in list(cls._tasks.keys()):
                task = cls._tasks[task_id]
                # 只清理已完成或失败的任务
                if task['status'] in ['completed', 'failed']:
                    # 可以添加一个时间阈值，例如只清理超过一定时间的任务
                    # 如果需要，可以取消下面的注释并调整时间
                    time_diff = (datetime.now() - task['start_time']).total_seconds()
                    if time_diff > 3600:  # 例如，只清理超过1小时的已完成任务
                        cls._tasks.pop(task_id)
                    logger.debug(f"Removed {task['status']} task: {task_id}")
                # 可以添加日志记录当前所有任务的状态，帮助调试
                else:
                    logger.debug(f"Keeping task {task_id} with status {task['status']}")
            final_count = len(cls._tasks)
            logger.info(f"Cleanup completed. Removed {initial_count - final_count} tasks")
    
    @classmethod
    async def clean_upload_files(cls):
        """清理upload中，10分钟之前的文件"""
        #遍历upload文件夹，删除10分钟之前的文件
        #获取当前时间
        now = datetime.now()
        #获取10分钟之前的时间
        ten_minutes_ago = now - timedelta(minutes=120)
        #遍历upload文件夹
        for file in os.listdir('upload'):
            #获取文件的创建时间
            file_time = datetime.fromtimestamp(os.path.getctime('upload/' + file))
            #如果文件的创建时间小于10分钟之前
            if file_time < ten_minutes_ago:
                #删除文件
                os.remove('upload/' + file)
                logger.info(f"Removed file: {file}")
        pass


    @classmethod
    async def start_workers(cls, num_workers: int = 2):
        """启动指定数量的工作协程"""
        if cls._workers_running:
            logger.warning("Workers already running")
            return
        
        logger.info(f"Starting {num_workers} worker coroutines")
        cls._workers_running = True
        for _ in range(num_workers):
            asyncio.create_task(cls._worker())