"""
任务管理器并发测试
用于验证修复后的并发问题
"""

import asyncio
import pytest
import tempfile
import os
from datetime import datetime, timedelta
from utils.task import TaskManager
from utils.task_monitor import task_monitor

class TestTaskManager:
    """任务管理器测试类"""
    
    @pytest.fixture(autouse=True)
    async def setup_and_teardown(self):
        """测试前后的设置和清理"""
        # 清理现有任务
        async with TaskManager._lock:
            TaskManager._tasks.clear()
        
        # 清理队列
        while not TaskManager._queue.empty():
            try:
                TaskManager._queue.get_nowait()
                TaskManager._queue.task_done()
            except asyncio.QueueEmpty:
                break
        
        yield
        
        # 测试后清理
        async with TaskManager._lock:
            TaskManager._tasks.clear()

    async def test_concurrent_task_creation(self):
        """测试并发任务创建"""
        async def dummy_task():
            await asyncio.sleep(0.1)
            return {"result": "success"}
        
        # 并发创建多个任务
        tasks = []
        for i in range(10):
            task_id = f"test_task_{i}"
            tasks.append(TaskManager.create_task(task_id, dummy_task()))
        
        # 等待所有任务创建完成
        await asyncio.gather(*tasks)
        
        # 验证任务都被正确创建
        stats = await TaskManager.get_stats()
        assert stats['total_tasks'] == 10
        assert stats['queued'] == 10

    async def test_task_cleanup_safety(self):
        """测试任务清理的线程安全性"""
        async def quick_task():
            return {"result": "completed"}
        
        # 创建一些任务
        for i in range(5):
            task_id = f"cleanup_test_{i}"
            await TaskManager.create_task(task_id, quick_task())
        
        # 手动设置一些任务为已完成状态（模拟旧任务）
        async with TaskManager._lock:
            for task_id, task_info in TaskManager._tasks.items():
                task_info['status'] = 'completed'
                task_info['start_time'] = datetime.now() - timedelta(hours=2)
        
        # 并发执行清理操作
        cleanup_tasks = [
            TaskManager.clean_completed_tasks(),
            TaskManager.clean_completed_tasks(),
            TaskManager.clean_completed_tasks()
        ]
        
        await asyncio.gather(*cleanup_tasks)
        
        # 验证清理后的状态
        stats = await TaskManager.get_stats()
        assert stats['total_tasks'] == 0  # 所有旧任务应该被清理

    async def test_file_cleanup_safety(self):
        """测试文件清理的线程安全性"""
        # 创建临时文件
        temp_files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(delete=False, dir='upload') as f:
                f.write(b"test content")
                temp_files.append(f.name)
        
        # 注册一个文件为活跃状态
        await TaskManager.register_active_file(temp_files[0])
        
        # 修改文件时间为2小时前
        old_time = datetime.now() - timedelta(hours=3)
        old_timestamp = old_time.timestamp()
        for file_path in temp_files:
            os.utime(file_path, (old_timestamp, old_timestamp))
        
        # 执行文件清理
        await TaskManager.clean_upload_files()
        
        # 验证活跃文件未被删除，其他文件被删除
        assert os.path.exists(temp_files[0])  # 活跃文件应该保留
        assert not os.path.exists(temp_files[1])  # 非活跃文件应该被删除
        assert not os.path.exists(temp_files[2])  # 非活跃文件应该被删除
        
        # 清理
        await TaskManager.unregister_active_file(temp_files[0])
        if os.path.exists(temp_files[0]):
            os.remove(temp_files[0])

    async def test_worker_state_consistency(self):
        """测试工作协程状态的一致性"""
        # 重置工作协程状态
        async with TaskManager._workers_lock:
            TaskManager._workers_running = False
        
        # 并发启动工作协程
        start_tasks = [
            TaskManager.start_workers(2),
            TaskManager.start_workers(2),
            TaskManager.start_workers(2)
        ]
        
        await asyncio.gather(*start_tasks)
        
        # 验证工作协程状态
        async with TaskManager._workers_lock:
            assert TaskManager._workers_running == True

    async def test_health_monitoring(self):
        """测试健康监控功能"""
        # 创建一些测试任务
        async def test_task():
            await asyncio.sleep(0.1)
            return {"result": "success"}
        
        for i in range(5):
            await TaskManager.create_task(f"health_test_{i}", test_task())
        
        # 获取健康报告
        health_report = await task_monitor.get_health_report()
        
        assert health_report.status is not None
        assert health_report.score >= 0
        assert health_report.stats.total_tasks == 5

    async def test_deadlock_detection(self):
        """测试死锁检测"""
        # 创建一个长时间运行的任务
        async def long_running_task():
            await asyncio.sleep(10)  # 模拟长时间运行
            return {"result": "completed"}
        
        task_id = "long_running_test"
        await TaskManager.create_task(task_id, long_running_task())
        
        # 手动设置任务为运行状态并修改开始时间
        async with TaskManager._lock:
            if task_id in TaskManager._tasks:
                TaskManager._tasks[task_id]['status'] = 'running'
                TaskManager._tasks[task_id]['start_time'] = datetime.now() - timedelta(minutes=35)
        
        # 检测死锁
        deadlocks = await task_monitor.detect_deadlocks()
        
        assert len(deadlocks) > 0
        assert task_id in str(deadlocks[0])

    async def test_concurrent_file_operations(self):
        """测试并发文件操作"""
        # 创建测试文件
        test_files = []
        for i in range(5):
            with tempfile.NamedTemporaryFile(delete=False, dir='upload') as f:
                f.write(b"test content")
                test_files.append(f.name)
        
        # 并发注册和取消注册文件
        register_tasks = [TaskManager.register_active_file(f) for f in test_files]
        await asyncio.gather(*register_tasks)
        
        unregister_tasks = [TaskManager.unregister_active_file(f) for f in test_files]
        await asyncio.gather(*unregister_tasks)
        
        # 验证文件状态
        stats = await TaskManager.get_stats()
        assert stats['active_files'] == 0
        
        # 清理测试文件
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)

# 运行测试的辅助函数
async def run_stress_test():
    """压力测试函数"""
    print("开始并发压力测试...")
    
    async def stress_task():
        await asyncio.sleep(0.01)
        return {"result": "stress_test_completed"}
    
    # 创建大量并发任务
    tasks = []
    for i in range(100):
        task_id = f"stress_test_{i}"
        tasks.append(TaskManager.create_task(task_id, stress_task()))
    
    await asyncio.gather(*tasks)
    
    # 等待一段时间让任务处理
    await asyncio.sleep(2)
    
    # 获取统计信息
    stats = await TaskManager.get_stats()
    print(f"压力测试完成: {stats}")
    
    # 执行清理
    await TaskManager.clean_completed_tasks()
    
    final_stats = await TaskManager.get_stats()
    print(f"清理后统计: {final_stats}")

if __name__ == "__main__":
    # 运行压力测试
    asyncio.run(run_stress_test())
