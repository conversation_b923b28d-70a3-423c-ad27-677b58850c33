# 并发修复部署指南

## 快速开始

### 1. 安装依赖（可选）
```bash
pip install psutil  # 用于系统监控，可选
```

### 2. 验证修复
启动应用后，检查健康状况：
```bash
curl http://localhost:8887/translate/system/health
```

预期响应：
```json
{
  "status": "healthy",
  "score": 95.0,
  "issues": [],
  "recommendations": [],
  "stats": {
    "total_tasks": 0,
    "queued_tasks": 0,
    "running_tasks": 0,
    "workers_running": true
  }
}
```

## 监控API使用

### 1. 系统健康检查
```bash
# 获取完整健康报告
curl http://localhost:8887/translate/system/health

# 响应示例
{
  "status": "healthy|warning|critical",
  "score": 85.5,
  "issues": ["队列积压: 25 个任务等待处理"],
  "recommendations": ["考虑增加工作协程数量"],
  "stats": {
    "total_tasks": 50,
    "queued_tasks": 25,
    "running_tasks": 2,
    "completed_tasks": 20,
    "failed_tasks": 3,
    "queue_size": 25,
    "workers_running": true,
    "active_files": 5,
    "avg_processing_time": 45.2,
    "memory_usage_mb": 256.8,
    "cpu_usage_percent": 15.3
  }
}
```

### 2. 系统统计信息
```bash
# 获取详细统计
curl http://localhost:8887/translate/system/stats

# 响应示例
{
  "total_tasks": 50,
  "queued": 25,
  "running": 2,
  "completed": 20,
  "failed": 3,
  "queue_size": 25,
  "workers_running": true,
  "active_files": 5
}
```

### 3. 死锁检测
```bash
# 检测潜在死锁
curl http://localhost:8887/translate/system/deadlocks

# 响应示例
{
  "deadlocks": [
    "任务 abc-123 运行时间过长: 0:35:42"
  ],
  "count": 1
}
```

## 性能调优

### 1. 工作协程数量调整
在 `main.py` 中调整：
```python
await TaskManager.start_workers(num_workers=4)  # 根据CPU核心数调整
```

### 2. 清理频率调整
在 `main.py` 中调整：
```python
await asyncio.sleep(300)  # 调整清理间隔（秒）
```

### 3. 任务清理阈值调整
在 `utils/task.py` 中调整：
```python
if time_diff > 3600:  # 调整任务保留时间（秒）
```

### 4. 文件清理时间调整
在 `utils/task.py` 中调整：
```python
cleanup_time_ago = now - timedelta(minutes=120)  # 调整文件保留时间
```

## 故障排除

### 1. 常见问题

#### 问题：任务队列积压
**症状：** `queue_size` 持续增长
**解决方案：**
- 增加工作协程数量
- 检查任务处理逻辑是否有阻塞
- 优化任务执行效率

#### 问题：内存使用过高
**症状：** `memory_usage_mb` 超过预期
**解决方案：**
- 减少任务清理阈值时间
- 增加清理频率
- 检查是否有内存泄漏

#### 问题：任务失败率高
**症状：** `failed_tasks` 比例过高
**解决方案：**
- 检查任务执行日志
- 验证外部依赖可用性
- 增加错误重试机制

### 2. 日志分析

#### 关键日志模式
```
# 正常启动
INFO - 任务处理工作协程已启动
INFO - 任务监控已启动
INFO - 定期清理任务已启动

# 健康状况
INFO - 系统健康状况: healthy (分数: 95.0)

# 清理操作
INFO - 开始执行定期清理...
INFO - Cleanup completed. Removed 5 tasks
INFO - File cleanup completed. Removed 3 files

# 潜在问题
WARNING - 发现问题: 队列积压: 25 个任务等待处理
ERROR - 检测到潜在死锁: 任务 abc-123 运行时间过长
```

### 3. 性能基准

#### 正常运行指标
- 健康分数：> 80
- 队列大小：< 20
- 平均处理时间：< 60秒
- 内存使用：< 500MB
- CPU使用：< 50%

#### 警告阈值
- 健康分数：60-80
- 队列大小：20-50
- 平均处理时间：60-300秒
- 内存使用：500-1000MB
- CPU使用：50-80%

#### 严重阈值
- 健康分数：< 60
- 队列大小：> 50
- 平均处理时间：> 300秒
- 内存使用：> 1000MB
- CPU使用：> 80%

## 测试验证

### 1. 功能测试
```bash
# 创建测试任务
curl -X POST http://localhost:8887/translate/word \
  -F "file=@test.docx" \
  -F "src=en" \
  -F "dst=zh"

# 检查任务状态
curl http://localhost:8887/translate/task/{task_id}
```

### 2. 压力测试
```python
import asyncio
import aiohttp

async def stress_test():
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(50):
            task = session.post(
                'http://localhost:8887/translate/text',
                data={'text': f'Test text {i}', 'src': 'en', 'dst': 'zh'}
            )
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        print(f"Completed {len(responses)} requests")

asyncio.run(stress_test())
```

### 3. 监控验证
```bash
# 持续监控健康状况
while true; do
  curl -s http://localhost:8887/translate/system/health | jq '.score'
  sleep 10
done
```

## 生产环境建议

### 1. 监控集成
- 集成到现有监控系统（如 Prometheus）
- 设置告警规则
- 建立仪表板

### 2. 日志管理
- 配置日志轮转
- 设置日志级别
- 建立日志聚合

### 3. 备份和恢复
- 定期备份配置
- 准备回滚方案
- 测试恢复流程

### 4. 安全考虑
- 限制监控API访问
- 配置防火墙规则
- 启用HTTPS

## 维护计划

### 1. 定期检查
- 每日检查健康状况
- 每周分析性能趋势
- 每月评估资源使用

### 2. 优化调整
- 根据使用模式调整参数
- 优化任务处理逻辑
- 更新监控阈值

### 3. 版本升级
- 测试新版本兼容性
- 制定升级计划
- 执行渐进式部署
